---18 july
ALTER TABLE PROMPT
  ADD COLUMN CUSTOMER_ID INT DEFAULT NULL COMMENT 'Associated customer ID',
  ADD COLUMN CREATOR INT DEFAULT NULL COMMENT 'Creator user ID',
  ADD COLUMN LAST_MODIFIER INT DEFAULT NULL COMMENT 'Last modifier user ID';

-- 21 july
ALTER TABLE TOOL
  ADD COLUMN CUSTOMER_ID INT DEFAULT NULL COMMENT 'Associated customer ID',
  ADD COLUMN CREATOR INT DEFAULT NULL COMMENT 'Creator user ID',
  ADD COLUMN LAST_MODIFIER INT DEFAULT NULL COMMENT 'Last modifier user ID';

ALTER TABLE QUERY
  ADD COLUMN CUSTOMER_ID INT DEFAULT NULL COMMENT 'Associated customer ID',
  ADD COLUMN CREATOR INT DEFAULT NULL COMMENT 'Creator user ID',
  ADD COLUMN LAST_MODIFIER INT DEFAULT NULL COMMENT 'Last modifier user ID';

ALTER TABLE TAG
  ADD COLUMN CUSTOMER_ID INT DEFAULT NULL COMMENT 'Associated customer ID',
  ADD COLUMN CREATOR INT DEFAULT NULL COMMENT 'Creator user ID',
  ADD COLUMN LAST_MODIFIER INT DEFAULT NULL COMMENT 'Last modifier user ID';

ALTER TABLE TESTCASE
  ADD COLUMN CUSTOMER_ID INT DEFAULT NULL COMMENT 'Associated customer ID',
  ADD COLUMN CREATOR INT DEFAULT NULL COMMENT 'Creator user ID',
  ADD COLUMN LAST_MODIFIER INT DEFAULT NULL COMMENT 'Last modifier user ID';

ALTER TABLE TEST_SUITE
  ADD COLUMN CUSTOMER_ID INT DEFAULT NULL COMMENT 'Associated customer ID',
  ADD COLUMN CREATOR INT DEFAULT NULL COMMENT 'Creator user ID',
  ADD COLUMN LAST_MODIFIER INT DEFAULT NULL COMMENT 'Last modifier user ID';

ALTER TABLE AGENT_TEST_CASES
  ADD COLUMN CUSTOMER_ID INT DEFAULT NULL COMMENT 'Associated customer ID',
  ADD COLUMN CREATOR INT DEFAULT NULL COMMENT 'Creator user ID',
  ADD COLUMN LAST_MODIFIER INT DEFAULT NULL COMMENT 'Last modifier user ID';

ALTER TABLE KNOWLEDGE_BASE
  ADD COLUMN CUSTOMER_ID INT DEFAULT NULL COMMENT 'Associated customer ID',
  ADD COLUMN CREATOR INT DEFAULT NULL COMMENT 'Creator user ID',
  ADD COLUMN LAST_MODIFIER INT DEFAULT NULL COMMENT 'Last modifier user ID';

ALTER TABLE MCP_SERVER
  ADD COLUMN CUSTOMER_ID INT DEFAULT NULL COMMENT 'Associated customer ID',
  ADD COLUMN CREATOR INT DEFAULT NULL COMMENT 'Creator user ID',
  ADD COLUMN LAST_MODIFIER INT DEFAULT NULL COMMENT 'Last modifier user ID';

ALTER TABLE TOOL ADD COLUMN INTENT_NAME VARCHAR(50) DEFAULT NULL COMMENT 'Intent name associated with x101 tool';
ALTER TABLE TOOL ADD COLUMN ENTITY_NAME VARCHAR(50) DEFAULT NULL COMMENT 'Entity name associated with x101 tool';
ALTER TABLE TOOL MODIFY COLUMN `TYPE` enum('AGENT','sourceCode','swaggerJson','workFlow','API','connector','agentBuilder', 'x101') DEFAULT NULL;

ALTER TABLE PROMPT_AUDIT ADD COLUMN START_TIME DATETIME(6) DEFAULT NULL COMMENT 'Start time of the prompt audit';
ALTER TABLE PROMPT_AUDIT ADD COLUMN END_TIME DATETIME(6) DEFAULT NULL COMMENT 'End time of the prompt audit';

ALTER TABLE TOOL_AUDIT ADD COLUMN START_TIME DATETIME(6) DEFAULT NULL COMMENT 'Start time of the tool audit';
ALTER TABLE TOOL_AUDIT ADD COLUMN END_TIME DATETIME(6) DEFAULT NULL COMMENT 'End time of the tool audit';

-- 20 August
ALTER TABLE PROMPT ADD COLUMN TOOL_CHOICE VARCHAR(25) DEFAULT 'auto' COMMENT 'Tool choice setting for the prompt (none, auto, required)';