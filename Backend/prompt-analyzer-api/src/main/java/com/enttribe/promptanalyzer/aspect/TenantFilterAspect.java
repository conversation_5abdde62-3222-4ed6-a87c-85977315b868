/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.aspect;

import com.enttribe.product.security.spring.userdetails.CustomerInfo;
import com.enttribe.promptanalyzer.constants.FilterConstants;
import com.enttribe.promptanalyzer.exception.BusinessException;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.hibernate.Filter;
import org.hibernate.Session;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.ResolvableType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Component;

/**
 * Aspect for applying tenant-based filtering to DAO operations in a multi-tenant environment.
 *
 * <p>This aspect automatically applies customer-specific filters to all DAO operations,
 * ensuring that data access is properly scoped to the current tenant. It uses Hibernate
 * filters to transparently add customer ID filtering to database queries.</p>
 *
 * <p>The aspect works by:</p>
 * <ul>
 *   <li>Intercepting all DAO method calls using AOP</li>
 *   <li>Checking if the entity extends BaseEntityGlobal</li>
 *   <li>Applying the customer ID filter if not already enabled</li>
 *   <li>Cleaning up filter state after operations complete</li>
 * </ul>
 *
 * <p>This aspect is only active when the SaaS mode is enabled via the
 * <code>prompt.saas.enable=true</code> configuration property.</p>
 *
 * <p><strong>Thread Safety:</strong> This aspect uses ThreadLocal to track filter state,
 * making it safe for use in multi-threaded environments.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 * @see FilterConstants#DEFAULT_CUSTOMER_ID_FILTER
 * @see CustomerInfo
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "prompt.saas", name = "enable", havingValue = "true")
public class TenantFilterAspect {

  private final CustomerInfo customerInfo;
  @PersistenceContext private EntityManager entityManager;

    public static final ThreadLocal<Boolean> filterState = ThreadLocal.withInitial(() -> Boolean.FALSE);

  /**
   * Applies tenant filtering before DAO method execution.
   *
   * <p>This method is executed before any DAO method call and ensures that
   * the appropriate customer ID filter is applied to the current Hibernate session.
   * It checks if the target entity extends BaseEntityGlobal and applies the filter
   * only if it hasn't been enabled already for the current thread.</p>
   *
   * @param joinPoint the join point representing the DAO method call
   * @throws BusinessException if there's an error applying the filter
   */
  @Before("execution(* com.enttribe.promptanalyzer.dao..*(..))")
    public void applyTenantFilter(JoinPoint joinPoint) {
        log.debug("called applyTenantFilter");
        Object target = joinPoint.getTarget();
        Class<?> daoClass = target.getClass();

        // Try to resolve the generic parameter (entity class)
        ResolvableType resolvableType = ResolvableType.forClass(daoClass).as(JpaRepository.class);
        Class<?> entityClass = resolvableType.getGeneric(0).resolve();
        Class<?> superclass = entityClass.getSuperclass();
        if (superclass == null) {
            return;
        }
        this.applyFilter(superclass.getName());
    }

    /**
     * Applies the customer ID filter to the current Hibernate session.
     *
     * <p>This method enables the customer ID filter on the current Hibernate session
     * if the entity type supports it (extends BaseEntityGlobal) and the filter hasn't
     * been enabled already for the current thread.</p>
     *
     * @param filterType the fully qualified class name of the entity type
     * @throws BusinessException if there's an error applying the filter
     */
    private void applyFilter(String filterType) {
        log.info("applyFilter called");

        try {
            if (filterType.contains("BaseEntityGlobal")) {
                Session session = this.entityManager.unwrap(Session.class);
                if (session != null && !filterState.get()) {
                    // Only enable the filter if it's not already enabled
                    log.info("_CUSTOMER_ID_FILTER: {}", this.customerInfo.getCustomerWrapper().getId());
                    Filter filter = session.enableFilter(FilterConstants.DEFAULT_CUSTOMER_ID_FILTER);
                    filter.setParameter("customerId", this.customerInfo.getCustomerWrapper().getId());
                    filterState.set(Boolean.TRUE);  // Track that the filter has been enabled
                }
            } else {
                log.info("BaseEntity not extended or not found");
            }
        } catch (Exception e) {
            log.error("error occurred in applyFilter : {}", e.getMessage(), e);
            throw new BusinessException(e.getMessage(),e);
        }
    }

    // You should ensure the filter is disabled at the end of the request or session
    /**
     * Resets the filter state after DAO method execution.
     *
     * <p>This method is executed after any DAO method call and cleans up the
     * ThreadLocal filter state to prevent memory leaks and ensure proper
     * filter behavior for subsequent operations.</p>
     */
    @After("execution(* com.enttribe.promptanalyzer.dao..*(..))")
    public static void resetFilterState() {
        filterState.remove();
    }
}
