/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.ai.dto.ChatCompletionRequestDto;
import com.enttribe.promptanalyzer.constants.APIConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;

import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * REST controller for managing LLM (Language Learning Model) API operations. Provides endpoints for
 * chat completions and prompt executions. All endpoints require appropriate security roles for
 * access.
 *
 * <AUTHOR>
 * @version 1.0
 */
@FeignClient(
    name = "LlmApiRest",
    url = "${prompt-analyzer-service.url}",
    path = "/chat",
    primary = false)
public interface LlmApiRest {

  /**
   * Executes chat completions using LLM API. Requires ROLE_API_CHAT_COMPLETE security role.
   *
   * @param requestDto The chat completion request parameters
   * @return Object containing the chat completion response
   * @apiNote Response Codes: 200 - Chat completion executed successfully 500 - Error occurred
   *     during execution
   */
  @Operation(
      summary = "Llm API chat completions",
      description = "Executes chat completions using LLM API with the provided request parameters.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_CHAT_COMPLETE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Llm API chat completions successfully execute"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/completions", consumes = MediaType.APPLICATION_JSON_VALUE)
  Object chatCompletion(@RequestBody ChatCompletionRequestDto requestDto);


    /**
   * Executes a prompt with variable substitution and optional formatting. Requires
   * ROLE_API_EXECUTE_PROMPT security role.
   *
   * @param requestDto Map containing promptId, variableMap, and format flag
   * @return Object containing the execution result
   * @apiNote Response Codes: 200 - Prompt executed successfully 500 - Error occurred during
   *     execution
   */
  @Operation(
      summary = "Execute Prompt",
      description =
          "Executes a prompt with variable substitution and optional formatting based on the provided parameters.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_EXECUTE_PROMPT})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Execute Prompt successfully."),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(
      path = "/executePrompt",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  Object executePrompt(@RequestBody Map<String, Object> requestDto);

  /**
   * Version 1 of prompt execution with variable substitution. Requires ROLE_API_EXECUTE_PROMPT_V1
   * security role. Returns result wrapped in a map with "result" key.
   *
   * @param requestDto Map containing promptId and variableMap
   * @return Map containing the execution result
   * @apiNote Response Codes: 200 - Prompt executed successfully 500 - Error occurred during
   *     execution
   */
  @Operation(
      summary = "Prompt Execution on Version v1",
      description =
          "Version 1 of prompt execution with variable substitution, returning result wrapped in a map with 'result' key.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_EXECUTE_PROMPT_V1})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Prompt Execution on Version v1 successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(
      path = "/v1/executePrompt",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  Object executePromptV1(@RequestBody Map<String, Object> requestDto);

  /**
   * Generates a system prompt based on the provided request parameters.
   *
   * @param request A map containing the request parameters for generating the system prompt
   * @return A map containing the generated system prompt
   */
  @Operation(
      summary = "Generate System Prompt",
      description = "Generates a system prompt based on the provided request parameters.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_GENERATE_SYSTEM_PROMPT})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "System prompt generated successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping("/generateSystemPrompt")
  Map<String, String> generateSystemPrompt(@RequestBody Map<String, String> request);

  /**
   * generate a new test case for given prompt. Requires ROLE_API_TESTCASE_WRITE security role.
   *
   * @param promptId The test case details to create
   * @return Map containing the result of the generate operation
   * @apiNote Response Codes: 200 - Create TestCase successfully 500 - Error occurred during
   *     creation
   */
  @Operation(
      summary = "Create TestCase",
      description =
          "Generates a new test case for the given prompt ID with the provided request parameters.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TESTCASE_GENERATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Generate TestCase successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/generateTestCase/{promptId}", consumes = MediaType.APPLICATION_JSON_VALUE)
  Map<String, Object> generateTestCase(
      @PathVariable String promptId, @RequestBody Map<String, String> request);
}
