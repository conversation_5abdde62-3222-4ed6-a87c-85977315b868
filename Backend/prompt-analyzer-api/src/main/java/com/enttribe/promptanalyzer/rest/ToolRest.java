/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.microintent.MicroIntentResponseDto;
import com.enttribe.promptanalyzer.dto.tool.SwaggerDto;
import com.enttribe.promptanalyzer.dto.tool.ToolConvertorDto;
import com.enttribe.promptanalyzer.dto.tool.ToolDto;
import com.enttribe.promptanalyzer.dto.tool.ToolDtoSdk;
import com.enttribe.promptanalyzer.dto.tool.ToolWorkflowDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * REST controller for managing tool operations. Provides endpoints for creating, updating,
 * searching, and managing tools, including support for Swagger integration, workflow generation,
 * and import/export functionality. All endpoints require appropriate security roles for access.
 *
 * <AUTHOR>
 * @version 1.0
 */
@FeignClient(
    name = "ToolRest",
    url = "${prompt-analyzer-service.url}",
    path = "/tool",
    primary = false)
public interface ToolRest {

  /**
   * Creates a new tool. Requires ROLE_API_TOOL_WRITE security role.
   *
   * @param toolDto The tool data to create
   * @return Map containing the result of the create operation
   * @apiNote Response Codes: 200 - Create Tool successfully 500 - Error occurred during creation
   */
  @Operation(
      summary = "Create new Tool",
      description = "Creates a new tool with the provided configuration and details.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TOOL_CREATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Create Tool successfully."),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/create", consumes = MediaType.APPLICATION_JSON_VALUE)
  Map<String, String> save(@Valid @RequestBody ToolDto toolDto);

  /**
   * Generates tools from Swagger JSON specification. Requires ROLE_API_TOOL_WRITE security role.
   *
   * @param swaggerDto The Swagger specification data
   * @return ResponseEntity containing the result of tool generation
   * @apiNote Response Codes: 200 - Tools created successfully with success/failure count 500 -
   *     Error occurred during generation
   */
  @PostMapping("/toolFromSwagger")
  @Operation(
      summary = "Generate Tool from Swagger JSON",
      description =
          "Generates tools from a provided Swagger JSON specification with configuration details.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TOOL_SWAGGER_CREATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Create Tool successfully by processing the provided Swagger JSON"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  ResponseEntity<Map<String, String>> createToolFromSwagger(@RequestBody SwaggerDto swaggerDto);

  /**
   * Searches for tools with pagination and sorting options. Requires ROLE_API_TOOL_READ security
   * role.
   *
   * @param filter Optional filter criteria for searching tools
   * @param offset Required pagination offset
   * @param size Required pagination size
   * @param orderBy Optional field to order results by
   * @param orderType Optional order direction (asc/desc)
   * @return List of matching tools as ToolConvertorDto objects
   */
  @GetMapping(path = "/search")
  @Operation(
      summary = "Search Tools",
      description =
          "Searches for tools with support for filtering, pagination, and sorting options.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TOOL_SEARCH})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description =
                "Search Tool successfully based on the provided parameters like filtering, and sorting"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  List<ToolConvertorDto> search(
      @RequestParam(required = false) String filter,
      @RequestParam(required = true) Integer offset,
      @RequestParam(required = true) Integer size,
      @RequestParam(required = false) String orderBy,
      @RequestParam(required = false) String orderType);

  /**
   * Counts the total number of tools matching the optional filter. Requires ROLE_API_TOOL_READ
   * security role.
   *
   * @param filter Optional filter criteria
   * @return Total count of matching tools
   * @apiNote Response Codes: 200 - Count All Tools successfully 500 - Error occurred during count
   *     operation
   */
  @GetMapping(path = "/count")
  @Operation(
      summary = "Count All Tools",
      description = "Counts the total number of tools matching the provided filter criteria.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TOOL_COUNT})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Count All Tools successfully."),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  Long count(@RequestParam(required = false) String filter);

  /**
   * Soft deletes a tool by its ID. Requires ROLE_API_TOOL_WRITE security role. Only the creator of
   * the tool can delete it.
   *
   * @param map Map containing the tool ID to delete
   * @return Map containing the result of the delete operation
   * @apiNote Response Codes: 200 - Delete Tool successfully 400 - Only creator can delete tool 500
   *     - Error occurred during deletion
   */
  @PostMapping(path = "/deleteById")
  @Operation(
      summary = "Delete Tool by Id (Creator Only)",
      description =
          "Deletes a tool identified by its ID from the system. Only the creator of the tool can perform this operation.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TOOL_DELETE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Delete Tool successfully."),
        @ApiResponse(
            responseCode = APIConstants.FORBIDDEN_CODE,
            description = APIConstants.FORBIDDEN_MESSAGE),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  Map<String, String> deleteById(@RequestBody Map<String, String> map);

  /**
   * Retrieves a tool by its ID. Requires ROLE_API_TOOL_READ security role.
   *
   * @param toolId The ID of the tool to retrieve
   * @return ToolConvertorDto containing the tool details
   * @apiNote Response Codes: 200 - Find Tool successfully 500 - Error occurred during retrieval
   */
  @Operation(
      summary = "Get Tool by Id",
      description = "Retrieves detailed information about a tool based on its ID.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TOOL_GET_BY_ID})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Find Tool successfully from Id"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping(path = "/findById/{toolId}")
  ToolConvertorDto getToolById(@PathVariable String toolId);

  /**
   * Gets x 101 tool list.
   *
   * @return the x 101 tool list
   */
  @Operation(
      summary = "Get x101 Tool list",
      description = "Retrieves intent and entity list from x101 and matches with tool list.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_X101_TOOL_GET})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Find Tool successfully from x101"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping(path = "/getX101ToolList")
  List<MicroIntentResponseDto> getX101ToolList();

  /**
   * Changes the status of a tool. Requires ROLE_API_TOOL_WRITE security role.
   *
   * @param requestMap Map containing tool ID and new status
   * @return Map containing the result of the status change operation
   * @apiNote Response Codes: 200 - Change Tool status successfully 500 - Error occurred during
   *     status change
   */
  @Operation(
      summary = "Change Tool status by Id",
      description = "Updates the status of a tool identified by its ID.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TOOL_CHANGE_STATUS})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Change Tool status successfully."),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/changeToolStatus")
  Map<String, String> changeToolStatus(@RequestBody Map<String, Object> requestMap);

  /**
   * Updates an existing tool. Requires ROLE_API_TOOL_WRITE security role.
   *
   * @param tool The updated tool data
   * @return Map containing the result of the update operation
   * @apiNote Response Codes: 200 - Update Tool successfully 500 - Error occurred during update
   */
  @PostMapping(path = "/update", consumes = MediaType.APPLICATION_JSON_VALUE)
  @Operation(
      summary = "Update Tool",
      description =
          "Updates an existing tool's configuration and details with the provided information.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TOOL_UPDATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Update Tool status successfully."),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  Map<String, String> update(@Valid @RequestBody ToolDto tool);

  /**
   * Creates tools from workflow data. Requires ROLE_API_TOOL_WRITE security role.
   *
   * @param toolWorkflowDto The workflow data to create tools from
   * @return ResponseEntity containing the result of tool creation
   * @apiNote Response Codes: 200 - Create Tool successfully from workflow 500 - Error occurred
   *     during creation
   */
  @PostMapping("/toolFromWorkflow")
  @Operation(
      summary = "Create Tools from workflow",
      description = "Creates tools based on the provided workflow configuration and details.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TOOL_WORKFLOW_CREATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Create Tool successfully from workflow data"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  ResponseEntity<Map<String, String>> createToolFromWorkflow(
      @RequestBody ToolWorkflowDto toolWorkflowDto);

  /**
   * Retrieves tools by their IDs for SDK integration. Requires ROLE_API_TOOL_READ security role.
   *
   * @param ids List of tool IDs to retrieve
   * @return List of tools as ToolDtoSdk objects
   * @apiNote Response Codes: 200 - Get Tools successfully 500 - Error occurred during retrieval
   */
  @PostMapping(path = "/getToolsByIds")
  @Operation(
      summary = "Get Tools by Ids",
      description = "Retrieves multiple tools based on their IDs for SDK integration.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TOOL_GET_BY_IDS})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Get Tools successfully by using list of ids for AI JAVA SDK"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  List<ToolDtoSdk> getToolsByIds(@RequestBody List<Integer> ids);

  /**
   * Retrieves tools by their IDs (version 1). Requires ROLE_API_TOOL_READ security role.
   *
   * @param ids List of tool IDs to retrieve
   * @return List of tools as ToolConvertorDto objects
   * @apiNote Response Codes: 200 - Get Tools successfully 500 - Error occurred during retrieval
   */
  @PostMapping(path = "/v1/getToolsByIds")
  @Operation(
      summary = "Get Tools by Ids (v1)",
      description = "Version 1 implementation of retrieving multiple tools by their IDs.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TOOL_GET_BY_IDS_V1})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Get Tools successfully by using list of ids"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  List<ToolConvertorDto> getToolsByIdsV1(@RequestBody List<String> ids);

  /**
   * Checks if provided source code compiles successfully. Requires ROLE_API_TOOL_READ security
   * role.
   *
   * @param requestMap Map containing sourceCode and className
   * @return Map containing compilation result (true/false)
   * @apiNote Response Codes: 200 - Compilation check completed 500 - Error occurred during check
   */
  @PostMapping(path = "/checkCompilation")
  @Operation(
      summary = "Check whether source code compiles or not",
      description = "Validates if the provided source code compiles successfully.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TOOL_CHECK_COMPILATION})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "returns true or false based on compilation of source code"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  Map<String, Object> checkCompilation(@RequestBody Map<String, String> requestMap);

  /**
   * Exports tools for an application as a downloadable resource.
   *
   * @param appName The application name to export tools for.
   * @return ResponseEntity containing the exported resource.
   */
  @Operation(
      summary = "Export tools for an application",
      description = "Exports all tools associated with a specific application for download.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TOOL_EXPORT})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Tools exported successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping(value = "/exportTool/{appName}", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  ResponseEntity<Resource> exportTool(@PathVariable String appName);

  /**
   * Imports tools from an uploaded file.
   *
   * @param file The multipart file containing tool data.
   * @return ResponseEntity containing the import result.
   */
  @Operation(
      summary = "Import tools from a file",
      description = "Imports tools from an uploaded file into the system.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TOOL_IMPORT})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Tools imported successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(value = "/importTool", produces = MediaType.MULTIPART_FORM_DATA_VALUE)
  ResponseEntity<Resource> importTool(@RequestParam("file") MultipartFile file);

  /**
   * Checks if a tool exists based on the provided criteria.
   *
   * @param requestMap A map containing the criteria to check for tool existence.
   * @return A map containing the result of the existence check.
   */
  @Operation(
      summary = "Check if a tool exists",
      description = "Verifies the existence of a tool based on the provided criteria.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TOOL_EXISTS})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Existence check completed successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/existsTool", consumes = MediaType.APPLICATION_JSON_VALUE)
  Map<String, Boolean> existsTool(@RequestBody Map<String, String> requestMap);

  /**
   * Updates a tag by its ID.
   *
   * @param id The ID of the tag to update.
   * @param tags A map containing the updated tag data.
   * @return A map containing the result of the update operation.
   */
  @Operation(
      summary = "Update tag by ID",
      description = "Updates the tags associated with a tool identified by its ID.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TOOL_UPDATE_TAG})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Tag updated successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/updateTagById/{id}")
  Map<String, String> updateTagById(@PathVariable String id, @RequestBody Map<String, String> tags);

  @Operation(
      summary = "Get Tool by Name",
      description = "Retrieves a tool's details based on its name.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TOOL_GET_BY_NAME})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Get Tool successfully by using tool name"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping(path = "/findByName/{toolName}")
  ToolDto getToolByName(@PathVariable String toolName);

  @PostMapping(path = "/findToolsByIds")
  List<ToolDto> findToolsByIds(@RequestBody List<Integer> ids);
}
