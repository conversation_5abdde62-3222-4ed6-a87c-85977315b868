/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.knowledge_base;

import com.enttribe.core.generic.utils.BypassValidation;
import java.util.List;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Range;
import org.springframework.web.multipart.MultipartFile;

/**
 * A Data Transfer Object (DTO) that encapsulates document upload and processing requests for the
 * knowledge base system. Handles various document types and configurations. Author: VisionWaves
 * Version: 1.0
 */
@Setter
@Getter
public class DocumentRequestDto {

  @Pattern(regexp = "^[a-zA-Z0-9_-]+$", message = "only a–z, 0–9, _ , and - are allowed")
  private String name;

  @NotEmpty(message = "Type is required")
  private String type;

  @NotEmpty(message = "Description is required")
  private String description;

  private String websiteUrl;
  private String collectionName;
  private String docType;

  @Range(min = 1, max = 15, message = "topK must be in the range of 1 to 15")
  @NotNull(message = "topK is required")
  private Integer topK;

  @NotNull(message = "Similarity threshold is required")
  private Double similarityThreshold;

  @BypassValidation(isScriptCheck = false)
  private List<MultipartFile> file;

  private Boolean deleted;
  private String tags;
  private String integration;
  private List<String> tables;

  @BypassValidation(isScriptCheck = false)
  private String content;

  private Boolean returnDirect;
}
