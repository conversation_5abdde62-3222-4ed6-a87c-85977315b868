/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.model;

import com.enttribe.core.generic.utils.ApplicationContextProvider;
import com.enttribe.product.security.spring.userdetails.CustomerInfo;
import com.enttribe.promptanalyzer.constants.FilterConstants;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.gson.annotations.Expose;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Transient;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Filter;
import org.hibernate.annotations.FilterDef;
import org.hibernate.annotations.ParamDef;
import org.hibernate.annotations.UpdateTimestamp;

@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@MappedSuperclass
@FilterDef(
    name = FilterConstants.DEFAULT_CUSTOMER_ID_FILTER,
    parameters = @ParamDef(name = "customerId", type = Integer.class))
@Filter(
    name = FilterConstants.DEFAULT_CUSTOMER_ID_FILTER,
    condition = "(CUSTOMER_ID = :customerId or CUSTOMER_ID = 1)")
@FilterDef(
    name = FilterConstants.CUSTOMER_ID_FILTER_LOCAL,
    parameters = @ParamDef(name = "customerId", type = Integer.class))
@Filter(name = FilterConstants.CUSTOMER_ID_FILTER_LOCAL, condition = "CUSTOMER_ID = :customerId")
@FilterDef(
    name = FilterConstants.CUSTOMER_ID_FILTER_GLOBAL,
    parameters = @ParamDef(name = "customerId", type = Integer.class))
@Filter(name = FilterConstants.CUSTOMER_ID_FILTER_GLOBAL, condition = "CUSTOMER_ID = 1")
@Slf4j
public class BaseEntityGlobal {

  @Basic
  @Expose
  @JsonIgnore
  @Column(name = "CUSTOMER_ID")
  protected Integer customerId;

  @Basic
  @Expose
  @Column(name = "MODIFIED_TIME", insertable = true, updatable = true)
  @UpdateTimestamp
  protected Date modifiedTime;

  @Basic
  @Expose
  @Column(name = "CREATED_TIME", insertable = true, updatable = false)
  @CreationTimestamp
  protected Date createdTime;

  @JsonIgnore
  @Column(name = "CREATOR")
  protected Integer creator;

  @JoinColumn(name = "LAST_MODIFIER", columnDefinition = "INT")
  @Column(name = "LAST_MODIFIER")
  @JsonIgnore
  protected Integer lastModifier;

  @JsonIgnore @Transient public Boolean skipPersist = false;

  @PrePersist
  void onCreate() {
    log.info("onCreate called  ");
    if (skipPersist != null && !this.skipPersist) {
      try {
        CustomerInfo customerInfo =
            ApplicationContextProvider.getApplicationContext().getBean(CustomerInfo.class);
        if (customerInfo != null) {
          this.customerId = customerInfo.getCustomerWrapper().getId();
          int userid = customerInfo.getUserId();
          log.info("when onCreate customerInfo is not null {}", customerInfo.getUsername());
          this.setCreator(userid);
          this.setLastModifier(userid);
          this.setCustomerId(this.customerId);
        }
      } catch (UnsupportedOperationException e) {
        log.error("Exception occurred : {}", e.getMessage(), e);
      }

      Date date = new Date();
      this.setCreatedTime(date);
      this.setModifiedTime(date);
    }
  }

  @PreUpdate
  void onUpdate() {
    if (skipPersist != null && !this.skipPersist) {
      log.info("onUpdate called  ");

      Integer loggedInCustomerId = null;
      try {
        CustomerInfo customerInfo =
            ApplicationContextProvider.getApplicationContext().getBean(CustomerInfo.class);
        if (customerInfo != null) {
          log.info("when onUpdate customerInfo is not null {}", customerInfo.getUsername());
          int userid = customerInfo.getUserId();
          loggedInCustomerId = customerInfo.getCustomerWrapper().getId();
          this.setLastModifier(userid);
        }
      } catch (UnsupportedOperationException e) {
        log.error("Exception occurred : {}", e.getMessage(), e);
      }

      log.info("After  onUpdate ");
      if (this.customerId == null) {
        this.setCustomerId(loggedInCustomerId);
      }
      this.setModifiedTime(new Date());
    }
  }
}
