/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.testcase;

import com.enttribe.core.generic.utils.BypassValidation;
import com.enttribe.promptanalyzer.dto.prompt.PromptConvertorDto;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that represents test case configurations for prompts. Manages test
 * case data, assertions, and associated prompt information. Author: VisionWaves Version: 1.0
 */
@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@Builder
public class TestCaseRequestDto {

  private String id;
  private String testcaseId;
  private Boolean deleted;

  @BypassValidation(isScriptCheck = false)
  private String inputJson;

  private String remark;
  private String assertions;
  private PromptConvertorDto prompt;
}
