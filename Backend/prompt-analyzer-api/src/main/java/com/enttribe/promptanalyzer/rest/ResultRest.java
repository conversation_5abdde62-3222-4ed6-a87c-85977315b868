/*
 * Copyright (c) 2024 Vwaves Technologies Private Limited
 *
 * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.
 * Product: Aspose.Total for Java
 * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations
 * Order ID: 250610115636
 *
 * This source code is part of a proprietary software system and may not be
 * distributed, modified, or reused without explicit written permission from
 * Vwaves Technologies Private Limited.
 *
 * Contact: <EMAIL>
 */
package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.result.AssertionResultResponseDto;
import com.enttribe.promptanalyzer.dto.result.TestCaseResultResponseDto;
import com.enttribe.promptanalyzer.dto.result.TestResultResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * REST controller for managing test result operations. Provides endpoints for searching and
 * counting various types of test results including assertion results, test case results, and
 * general test results.
 *
 * <p>This interface provides comprehensive functionality for retrieving and analyzing test
 * execution results with support for filtering, pagination, and sorting capabilities. All
 * endpoints require appropriate security roles for access.</p>
 *
 * <p>Key responsibilities:</p>
 * <ul>
 *   <li>Search and retrieve assertion results with filtering and pagination</li>
 *   <li>Search and retrieve test case results with filtering and pagination</li>
 *   <li>Search and retrieve general test results with filtering and pagination</li>
 *   <li>Count results for each type with optional filtering</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 */
@FeignClient(
    name = "ResultRest",
    url = "${prompt-analyzer-service.url}",
    path = "/result",
    primary = false)
public interface ResultRest {

  /**
   * Searches for assertion results with pagination, filtering, and sorting capabilities.
   * Requires ROLE_API_RESULT_SEARCH security role.
   *
   * @param filter optional filter criteria for searching assertion results
   * @param offset required pagination offset (zero-based)
   * @param size required pagination size
   * @param orderBy optional field name to sort by
   * @param orderType optional sort direction ("asc" or "desc")
   * @return a list of assertion result response DTOs matching the criteria
   */
  @Operation(
      summary = "Search assertion results",
      description = "Searches for assertion results with optional filtering, and ordering.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_RESULT_SEARCH})
      })
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Assertion results fetched successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request parameters")
      })
  @GetMapping(path = "/assertion/search")
  List<AssertionResultResponseDto> searchAssertionResults(
      @RequestParam(required = false) String filter,
      @RequestParam Integer offset,
      @RequestParam Integer size,
      @RequestParam(required = false) String orderBy,
      @RequestParam(required = false) String orderType);

  /**
   * Counts assertion results matching the optional filter criteria.
   * Requires ROLE_API_ASSERTION_RESULT_COUNT security role.
   *
   * @param filter optional filter criteria for counting assertion results
   * @return the total count of matching assertion results
   */
  @Operation(
      summary = "Count assertion results",
      description = "Counts assertion results with optional filtering.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_ASSERTION_RESULT_COUNT})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "200",
            description = "Assertion results count fetched successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request parameters")
      })
  @GetMapping(path = "/assertion/count")
  Long countAssertionResults(@RequestParam(required = false) String filter);

  /**
   * Searches for test case results with pagination, filtering, and sorting capabilities.
   * Requires ROLE_API_RESULT_TESTCASE_SEARCH security role.
   *
   * @param filter optional filter criteria for searching test case results
   * @param offset required pagination offset (zero-based)
   * @param size required pagination size
   * @param orderBy optional field name to sort by
   * @param orderType optional sort direction ("asc" or "desc")
   * @return a list of test case result response DTOs matching the criteria
   */
  @Operation(
      summary = "Search test case results",
      description =
          "Searches for test case results with optional filtering, pagination, and ordering.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_RESULT_TESTCASE_SEARCH})
      })
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Test case results fetched successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request parameters")
      })
  @GetMapping(path = "/testcase/search")
  List<TestCaseResultResponseDto> searchTestCaseResults(
      @RequestParam(required = false) String filter,
      @RequestParam Integer offset,
      @RequestParam Integer size,
      @RequestParam(required = false) String orderBy,
      @RequestParam(required = false) String orderType);

  /**
   * Counts test case results matching the optional filter criteria.
   * Requires ROLE_API_RESULT_COUNT security role.
   *
   * @param filter optional filter criteria for counting test case results
   * @return the total count of matching test case results
   */
  @Operation(
      summary = "Count test case results",
      description = "Counts test case results with optional filtering.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_RESULT_COUNT})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "200",
            description = "Test case results count fetched successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request parameters")
      })
  @GetMapping(path = "/testcase/count")
  Long countTestCaseResults(@RequestParam(required = false) String filter);

  /**
   * Searches for test results with pagination, filtering, and sorting capabilities.
   * Requires ROLE_API_TEST_SEARCH security role.
   *
   * @param filter optional filter criteria for searching test results
   * @param offset required pagination offset (zero-based)
   * @param size required pagination size
   * @param orderBy optional field name to sort by
   * @param orderType optional sort direction ("asc" or "desc")
   * @return a list of test result response DTOs matching the criteria
   */
  @Operation(
      summary = "Search test results",
      description = "Searches for test results with optional filtering, pagination, and ordering.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TEST_SEARCH})
      })
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Test results fetched successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request parameters")
      })
  @GetMapping(path = "/test/search")
  List<TestResultResponseDto> searchTestResults(
      @RequestParam(required = false) String filter,
      @RequestParam Integer offset,
      @RequestParam Integer size,
      @RequestParam(required = false) String orderBy,
      @RequestParam(required = false) String orderType);

  /**
   * Counts test results matching the optional filter criteria.
   * Requires ROLE_API_TEST_COUNT security role.
   *
   * @param filter optional filter criteria for counting test results
   * @return the total count of matching test results
   */
  @Operation(
      summary = "Count test results",
      description = "Counts test results with optional filtering.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TEST_COUNT})
      })
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Test results count fetched successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request parameters")
      })
  @GetMapping(path = "/test/count")
  Long countTestResults(@RequestParam(required = false) String filter);
}
