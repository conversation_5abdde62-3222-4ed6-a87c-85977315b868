/*

 * Copyright (c) 2024 Vwaves Technologies Private Limited

 *

 * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

 * Product: Aspose.Total for Java

 * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

 * Order ID: 250610115636

 *

 * This source code is part of a proprietary software system and may not be

 * distributed, modified, or reused without explicit written permission from

 * Vwaves Technologies Private Limited.

 *

 * Contact: <EMAIL>

 */
package com.enttribe.promptanalyzer.dto.audit;

import com.enttribe.core.generic.utils.BypassValidation;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;
import java.util.Map;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that captures and transfers exception-related audit information.
 * This class provides a structured format for logging and tracking exceptions across the system,
 * including contextual information about the exception occurrence. Author: VisionWaves Version: 1.0
 */
@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExceptionAuditDto {

    private Date timestamp;

    @BypassValidation(isScriptCheck = false)
    private String customMessage;
    @BypassValidation(isScriptCheck = false)
    private String exceptionMessage;
    @BypassValidation(isScriptCheck = false)
    private String exceptionTrace;
    @BypassValidation(isScriptCheck = false)
    private String methodName;
    @BypassValidation(isScriptCheck = false)
    private Map<String, Object> methodParameters;
    @BypassValidation(isScriptCheck = false)
    private Map<String, Object> identifier;
    private String applicationName;
    private String promptId;
    private String category;
    private String auditId;

}
