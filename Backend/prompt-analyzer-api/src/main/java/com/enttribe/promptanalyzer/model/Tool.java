/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.model;

import com.enttribe.promptanalyzer.util.ByteCodeMapConverter;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Lob;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * Entity representing a tool configuration. Stores details about tools, including their properties,
 * associations, and configurations.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@Setter
@Entity
@Table(name = "TOOL")
public class Tool extends BaseEntityGlobal {

  /** Unique ID of the tool. */
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Id
  @Column(name = "ID", columnDefinition = "INT")
  private Integer id;

  /** Unique ID of the tool. */
  @Column(name = "NANO_ID", length = 50)
  private String nanoId;

  /** Name of the application associated with the tool. */
  @Column(name = "APPLICATION_NAME", length = 40)
  private String applicationName;

  /** Category of the tool. */
  @Column(name = "CATEGORY", length = 20)
  private String category;

  /** Name of the tool. */
  @Column(name = "TOOL_NAME", length = 70)
  private String toolName;

  /** Display name of the tool. */
  @Column(name = "DISPLAY_NAME")
  private String displayName;

  /** Version of the tool. */
  @Column(name = "VERSION", length = 10)
  private String version;

  /** Indicates if the tool is marked as deleted. */
  @Column(name = "DELETED")
  private Boolean deleted = false;

  /** Unique identifier for the tool. */
  @Column(name = "TOOL_ID")
  private String toolId;

  /** Language associated with the tool. */
  @Column(name = "LANGUAGE", length = 40)
  private String language;

  /** Type of the tool (e.g., AGENT, sourceCode, swaggerJson, workFlow). */
  @Column(
      name = "TYPE",
      columnDefinition =
          "ENUM('AGENT','sourceCode','swaggerJson','workFlow','API','connector','agentBuilder')")
  private String type;

  /** Byte code map for the tool. */
  @Lob
  @Convert(converter = ByteCodeMapConverter.class)
  @Column(name = "BYTE_CODE_MAP", columnDefinition = "LONGTEXT")
  private Map<String, byte[]> byteCodeMap = new HashMap<>();

  /** Associated prompt for the tool. */
  @ManyToOne
  @JoinColumn(name = "PROMPT_ID")
  private Prompt prompt;

  /** Source code of the tool. */
  @Lob
  @Column(name = "SOURCE_CODE", columnDefinition = "LONGTEXT")
  private String sourceCode;

  /** Description of the tool. */
  @Column(name = "DESCRIPTION", length = 1200)
  private String description;

  /** Class name associated with the tool. */
  @Column(name = "CLASS_NAME", length = 100)
  private String className;

  /** Request type for the tool. */
  @Column(name = "REQUEST_TYPE", length = 100)
  private String requestType;

  /** JSON configuration of the tool. */
  @Column(name = "TOOL_JSON", columnDefinition = "TEXT")
  private String toolJson;

  /** URL associated with the tool. */
  @Column(name = "URL")
  private String url;

  /** HTTP method used by the tool. */
  @Column(name = "HTTP_METHOD", length = 10)
  private String httpMethod;

  /** Parameters for the tool. */
  @Column(name = "PARAMETERS", columnDefinition = "TEXT")
  private String parameters;

  @Column(name = "AUTH_TYPE")
  private String authType;

  /** Authentication key for the tool. */
  @Column(name = "AUTH_KEY")
  private String authKey;

  /** Authentication value for the tool. */
  @Column(name = "AUTH_VALUE")
  private String authValue;

  /** Image associated with the tool. */
  @Column(name = "TOOL_IMAGE", columnDefinition = "TEXT")
  private String toolImage;

  /** Tags associated with the tool. */
  @Column(name = "TAGS")
  private String tags;

  /** Status of the tool (e.g., DRAFT, PUBLISH, ARCHIVE, ACTIVE, DEACTIVE). */
  @Column(
      name = "STATUS",
      columnDefinition = "ENUM('DRAFT','PUBLISH','ARCHIVE','ACTIVE','DEACTIVE')")
  private String status;

  /** ID of the associated agent. */
  @Column(name = "AGENT_ID")
  private Long agentId;

  /** Additional API configuration. */
  @Column(name = "API_TOOL_INFO")
  private String apiToolInfo;

  @Column(name = "RETURN_DIRECT")
  private Boolean returnDirect = false;

  @Column(name = "INTENT_NAME", length = 50)
  private String intentName;

  @Column(name = "ENTITY_NAME", length = 50)
  private String entityName;
}
