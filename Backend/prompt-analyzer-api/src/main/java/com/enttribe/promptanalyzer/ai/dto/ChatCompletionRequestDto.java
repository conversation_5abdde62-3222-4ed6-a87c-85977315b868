/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.ai.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * Data Transfer Object for chat completion requests. Contains configuration parameters for AI chat
 * interactions. Author: VisionWaves Version: 1.0
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChatCompletionRequestDto {

  private Integer maxTokens;
  private List<MessageDto> messages;
  private String model;
  private Double temperature;
  private Double topP;
  private Boolean jsonMode;
  private String provider;
  private String reasoningEffort;
  private Boolean skipGuard = false;
}
