/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.constants;

import com.enttribe.promptanalyzer.aspect.TenantFilterAspect;

/**
 * Constants for Hibernate filter names used in multi-tenant data filtering.
 *
 * <p>This class defines the filter names used by the tenant filtering system
 * to ensure proper data isolation between different customers in a multi-tenant
 * environment.</p>
 *
 * <p>The filters are used in conjunction with the {@link TenantFilterAspect}
 * to automatically apply customer-specific filtering to all database queries.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 * @see TenantFilterAspect
 */
public class FilterConstants {

  /**
   * Default customer ID filter name used for standard tenant filtering.
   * This filter is applied to all entities that extend BaseEntityGlobal.
   */
  public static final String DEFAULT_CUSTOMER_ID_FILTER = "_CUSTOMER_ID_FILTER";

  /**
   * Local customer ID filter name for session-specific filtering.
   * Used when filtering needs to be applied only to the current session.
   */
  public static final String CUSTOMER_ID_FILTER_LOCAL = "CUSTOMER_ID_FILTER_LOCAL";

  /**
   * Global customer ID filter name for application-wide filtering.
   * Used when filtering needs to be applied across the entire application.
   */
  public static final String CUSTOMER_ID_FILTER_GLOBAL = "CUSTOMER_ID_FILTER_GLOBAL";

  /**
   * Private constructor to prevent instantiation of this utility class.
   *
   * @throws IllegalStateException if an attempt is made to instantiate this class
   */
  private FilterConstants() {
    throw new IllegalStateException("Utility class cannot be instantiated");
  }
}
