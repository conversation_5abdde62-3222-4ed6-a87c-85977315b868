/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.agent;

import com.enttribe.core.generic.utils.BypassValidation;
import com.enttribe.promptanalyzer.dto.assertion.AssertionRequestDto;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class AgentTestCaseRequestDto {

  private String id;

  @NotBlank(message = "Name is required")
  private String name;

  private String description;

    @BypassValidation(isScriptCheck = false)
  private String history;

  private String advisors;
  private Boolean deleted;
  private String agentId;
  private String agentType;
  private String agentName;
  @Valid private List<AssertionRequestDto> assertions;

  @NotBlank(message = "user question is required")
  private String userQuestion;

    @BypassValidation(isScriptCheck = false)
  private Map<String, Object> variableMap;
}
