/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.exception;

/**
 * Exception thrown when a requested resource cannot be found in the system.
 *
 * <p>This exception is typically thrown when attempting to retrieve, update, or delete
 * a resource (such as a tool, prompt, agent, or knowledge base) that does not exist
 * in the database or storage system.</p>
 *
 * <p>Common scenarios where this exception is thrown:</p>
 * <ul>
 *   <li>Attempting to retrieve a tool by ID that doesn't exist</li>
 *   <li>Updating a prompt that has been deleted</li>
 *   <li>Accessing a knowledge base entry that was removed</li>
 *   <li>Referencing an agent that no longer exists</li>
 * </ul>
 *
 * <p>This exception extends {@link RuntimeException} and is therefore an unchecked exception,
 * meaning it does not need to be explicitly caught or declared in method signatures.</p>
 *
 * <AUTHOR> <PERSON>gi
 * @version 1.0
 * @since 1.0
 */
public class ResourceNotFoundException extends RuntimeException {

  /**
   * Constructs a new ResourceNotFoundException with the specified detail message.
   *
   * <p>The detail message is saved for later retrieval by the {@link #getMessage()} method.</p>
   *
   * @param message the detail message (which is saved for later retrieval by the getMessage() method)
   */
  public ResourceNotFoundException(String message) {
    super(message);
  }

  /**
   * Constructs a new ResourceNotFoundException with the specified detail message and cause.
   *
   * <p>Note that the detail message associated with cause is not automatically incorporated
   * in this exception's detail message.</p>
   *
   * @param message the detail message (which is saved for later retrieval by the getMessage() method)
   * @param cause the cause (which is saved for later retrieval by the getCause() method)
   */
  public ResourceNotFoundException(String message, Throwable cause) {
    super(message, cause);
  }
}
