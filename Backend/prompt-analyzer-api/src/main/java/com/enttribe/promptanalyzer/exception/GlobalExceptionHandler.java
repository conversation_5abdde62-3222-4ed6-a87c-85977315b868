/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.exception;

import jakarta.persistence.PersistenceException;
import java.util.Date;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

/**
 * Global exception handler for handling exceptions across the application. Provides specific
 * handlers for different exception types to return appropriate HTTP responses.
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

  private static final Logger LOG = LoggerFactory.getLogger(GlobalExceptionHandler.class);

  /**
   * Handles ResourceNotFoundException and returns a NOT_FOUND response.
   *
   * @param ex the ResourceNotFoundException
   * @param request the web request
   * @return a ResponseEntity with error details and NOT_FOUND status
   */
  @ExceptionHandler(ResourceNotFoundException.class)
  public ResponseEntity<ErrorDetails> handlerResourceNotFoundException(
      ResourceNotFoundException ex, WebRequest request) {
    ErrorDetails errorDetails =
        new ErrorDetails(new Date(), ex.getMessage(), request.getDescription(false));
    return new ResponseEntity<>(errorDetails, HttpStatus.NOT_FOUND);
  }

  /**
   * Handles BusinessException and returns a BAD_REQUEST response.
   *
   * @param ex the BusinessException
   * @param request the web request
   * @return a ResponseEntity with error details and BAD_REQUEST status
   */
  @ExceptionHandler(BusinessException.class)
  public ResponseEntity<ErrorDetails> handlerBusinessException(
      BusinessException ex, WebRequest request) {
    Throwable cause = ex.getCause();
    boolean causeExist = ex.getCause() != null;
    if (causeExist
        && (cause instanceof PersistenceException || cause instanceof DataAccessException)) {
      String userMessage = "A database error occurred. Please contact administrator.";
      LOG.error("Database error occurred: {}", ex.getMessage(), ex);
      ErrorDetails errorDetails =
          new ErrorDetails(new Date(), userMessage, request.getDescription(false), null);
      return new ResponseEntity<>(errorDetails, HttpStatus.INTERNAL_SERVER_ERROR);
    }
    ErrorDetails errorDetails;
    if (cause != null) {
      errorDetails =
          new ErrorDetails(
              new Date(), ex.getMessage(), request.getDescription(false), cause.getMessage());
    } else {
      errorDetails = new ErrorDetails(new Date(), ex.getMessage(), request.getDescription(false));
    }
    return new ResponseEntity<>(errorDetails, HttpStatus.BAD_REQUEST);
  }

  /**
   * Handles DuplicateResourceException and returns a CONFLICT response. This is used when
   * attempting to create a resource with a name that already exists.
   *
   * @param ex the DuplicateResourceException
   * @param request the web request
   * @return a ResponseEntity with error details and CONFLICT status
   */
  @ExceptionHandler(DuplicateResourceException.class)
  public ResponseEntity<ErrorDetails> handlerDuplicateResourceException(
      DuplicateResourceException ex, WebRequest request) {
    LOG.warn("Duplicate resource name attempted: {}", ex.getMessage());
    ErrorDetails errorDetails =
        new ErrorDetails(new Date(), ex.getMessage(), request.getDescription(false));
    return new ResponseEntity<>(errorDetails, HttpStatus.CONFLICT);
  }

  /**
   * Handles UnsupportedOperationException and returns a FORBIDDEN response. This is used for
   * authorization failures where the user is not allowed to perform an operation.
   *
   * @param ex the UnsupportedOperationException
   * @param request the web request
   * @return a ResponseEntity with error details and FORBIDDEN status
   */
  @ExceptionHandler(UnsupportedOperationException.class)
  public ResponseEntity<ErrorDetails> handlerUnsupportedOperationException(
      UnsupportedOperationException ex, WebRequest request) {
    LOG.warn("Unsupported operation attempted: {}", ex.getMessage());
    ErrorDetails errorDetails =
        new ErrorDetails(new Date(), ex.getMessage(), request.getDescription(false));
    return new ResponseEntity<>(errorDetails, HttpStatus.FORBIDDEN);
  }

  /**
   * Handles generic Exception and returns an INTERNAL_SERVER_ERROR response.
   *
   * @param ex the Exception
   * @param request the web request
   * @return a ResponseEntity with error details and INTERNAL_SERVER_ERROR status
   */
  @ExceptionHandler(Exception.class)
  public ResponseEntity<ErrorDetails> handlerException(Exception ex, WebRequest request) {
    boolean causeExist = ex.getCause() != null;
    if (ex instanceof PersistenceException
        || (causeExist
            && (ex.getCause() instanceof PersistenceException
                || ex.getCause() instanceof DataAccessException))) {
      String userMessage = "A database error occurred. Please contact administrator.";
      LOG.error("Database error occurred: {}", ex.getMessage(), ex);
      ErrorDetails errorDetails =
          new ErrorDetails(new Date(), userMessage, request.getDescription(false), null);
      return new ResponseEntity<>(errorDetails, HttpStatus.INTERNAL_SERVER_ERROR);
    }
    ErrorDetails errorDetails =
        new ErrorDetails(new Date(), ex.getMessage(), request.getDescription(false));

    if (ex.getMessage().contains("No static resource"))
      return new ResponseEntity<>(errorDetails, HttpStatus.NOT_FOUND);

    return new ResponseEntity<>(errorDetails, HttpStatus.INTERNAL_SERVER_ERROR);
  }

  @ExceptionHandler(MethodArgumentNotValidException.class)
  public ResponseEntity<ErrorDetails> handleValidationException(
      MethodArgumentNotValidException ex, WebRequest request) {

    String errors =
        ex.getBindingResult().getFieldErrors().stream()
            .map(err -> err.getField() + ": " + err.getDefaultMessage())
            .collect(Collectors.joining("; "));

    ErrorDetails errorDetails = new ErrorDetails(new Date(), "Validation failed", errors);

    return new ResponseEntity<>(errorDetails, HttpStatus.BAD_REQUEST);
  }

  /**
   * Handles MethodArgumentNotValidException and returns a BAD_REQUEST response with validation
   * errors.
   *
   * @param ex the AccessDeniedException
   * @return a ResponseEntity with validation errors and BAD_REQUEST status
   */
  @ExceptionHandler(AccessDeniedException.class)
  public ResponseEntity<ErrorDetails> handleAccessDeniedException(
      AccessDeniedException ex, WebRequest request) {
    ErrorDetails errorDetails =
        new ErrorDetails(
            new Date(),
            ex.getMessage(), // Shows "Access denied: only the creator can delete this resource."
            request.getDescription(false));
    return new ResponseEntity<>(errorDetails, HttpStatus.FORBIDDEN);
  }
}
