/*
 * Copyright (c) 2024 Vwaves Technologies Private Limited
 *
 * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.
 * Product: Aspose.Total for Java
 * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations
 * Order ID: 250610115636
 *
 * This source code is part of a proprietary software system and may not be
 * distributed, modified, or reused without explicit written permission from
 * Vwaves Technologies Private Limited.
 *
 * Contact: <EMAIL>
 */
package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.testsuite.TestSuiteRequestDto;
import com.enttribe.promptanalyzer.dto.testsuite.TestSuiteResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * REST controller for managing test suite operations. Provides endpoints for creating, updating,
 * searching, and managing test suites with support for import/export functionality.
 *
 * <p>This interface provides comprehensive functionality for test suite lifecycle management
 * including CRUD operations, bulk operations, and data import/export capabilities. All endpoints
 * require appropriate security roles for access.</p>
 *
 * <p>Key responsibilities:</p>
 * <ul>
 *   <li>Create and update test suites with validation</li>
 *   <li>Search and retrieve test suites with filtering and pagination</li>
 *   <li>Delete test suites by unique identifier</li>
 *   <li>Add agent test cases to existing test suites</li>
 *   <li>Export test cases from test suites</li>
 *   <li>Import test cases into test suites from files</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 */
@FeignClient(
    name = "TestSuiteRest",
    url = "${prompt-analyzer-service.url}",
    path = "/testSuite",
    primary = false)
public interface TestSuiteRest {

  /**
   * Creates a new test suite. Requires ROLE_API_TESTSUITE_CREATE security role.
   *
   * @param requestDto the test suite request DTO containing creation parameters
   * @return a map containing the result of the creation operation
   */
  @Operation(
      summary = "Create Test Suite",
      description = "Creates a new test suite.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TESTSUITE_CREATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Created successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping("/create")
  Map<String, String> create(@RequestBody TestSuiteRequestDto requestDto);

  /**
   * Updates an existing test suite. Requires ROLE_API_TESTSUITE_UPDATE security role.
   *
   * @param requestDto the test suite request DTO containing update parameters
   * @return a map containing the result of the update operation
   */
  @Operation(
      summary = "Update Test Suite",
      description = "Updates an existing test suite.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TESTSUITE_UPDATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Updated successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping("/update")
  Map<String, String> update(@RequestBody TestSuiteRequestDto requestDto);

  /**
   * Searches for test suites with pagination, filtering, and sorting capabilities.
   * Requires ROLE_API_TESTSUITE_SEARCH security role.
   *
   * @param filter optional filter criteria for searching test suites
   * @param offset the pagination offset (zero-based)
   * @param size the number of results to return per page
   * @param orderBy optional field name to sort by
   * @param orderType optional sort direction ("asc" or "desc")
   * @return a list of test suite response DTOs matching the criteria
   */
  @Operation(
      summary = "Search Test Suites",
      description = "Searches for test suites with optional filtering.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TESTSUITE_SEARCH})
      })
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Search successful"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping("/search")
  List<TestSuiteResponseDto> search(
      @RequestParam(required = false) String filter,
      @RequestParam(required = false) Integer offset,
      @RequestParam(required = false) Integer size,
      @RequestParam(required = false) String orderBy,
      @RequestParam(required = false) String orderType);

  /**
   * Counts the total number of test suites matching the filter criteria.
   * Requires ROLE_API_TESTSUITE_COUNT security role.
   *
   * @param filter optional filter criteria for counting test suites
   * @return the total count of matching test suites
   */
  @Operation(
      summary = "Count Test Suites",
      description = "Counts the number of test suites matching the filter.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TESTSUITE_COUNT})
      })
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Count successful"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping("/count")
  Long count(@RequestParam(required = false) String filter);

  /**
   * Deletes a test suite by its unique identifier. Only the creator can perform this operation.
   * Requires ROLE_API_TESTSUITE_DELETE security role.
   *
   * @param id the unique identifier of the test suite to delete
   * @return a map containing the result of the delete operation
   */
  @Operation(
      summary = "Delete Test Suite by Id (Creator Only)",
      description =
          "Deletes a test suite identified by its ID from the system. Only the creator of the test suite can perform this operation.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TESTSUITE_DELETE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Delete Test Suite successfully."),
        @ApiResponse(
            responseCode = APIConstants.FORBIDDEN_CODE,
            description = APIConstants.FORBIDDEN_MESSAGE),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping("/delete/{id}")
  Map<String, String> deleteById(@PathVariable("id") String id);

  /**
   * Adds agent test cases to an existing test suite. Requires ROLE_API_ADD_AGENT_TEST_CASES
   * security role.
   *
   * @param testSuiteRequestDto the test suite request DTO containing the test cases to add
   * @return a map containing the result of the add operation
   */
  @Operation(
      summary = "Add Agent TestCases to Test Suite",
      description = "Adds multiple AgentTestCases to an existing Test Suite.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_ADD_AGENT_TEST_CASES})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "AgentTestCases added successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping("/addAgentTestCases")
  Map<String, String> addAgentTestCasesToSuite(
      @Valid @RequestBody TestSuiteRequestDto testSuiteRequestDto);

  /**
   * Exports test cases from a specific test suite as a downloadable resource.
   * Requires ROLE_API_TEST_SUITE_EXPORT security role.
   *
   * @param suiteId the unique identifier of the test suite to export
   * @return a ResponseEntity containing the exported resource
   */
  @Operation(
      summary = "Export Test Cases by Suite Id",
      description = "Exports all test cases in a suite as CSV, including assertion details.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TEST_SUITE_EXPORT})
      })
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Export successful"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping(
      value = "/exportTestCases/{suiteId}",
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  ResponseEntity<org.springframework.core.io.Resource> exportTestCasesBySuiteId(
      @PathVariable("suiteId") String suiteId);

  /**
   * Imports test cases into a test suite from an uploaded file. Requires ROLE_API_TEST_SUITE_IMPORT
   * security role.
   *
   * @param suiteId the unique identifier of the test suite to import into
   * @param file the multipart file containing the test cases to import
   * @return a ResponseEntity containing the import result
   * @throws IOException if an error occurs during file processing
   */
  @Operation(
      summary = "Import Test Cases to Suite",
      description = "Imports test cases from a CSV file and adds them to a suite.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TEST_SUITE_IMPORT})
      })
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Import successful"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(
      value = "/importTestCases/{suiteId}",
      consumes = MediaType.MULTIPART_FORM_DATA_VALUE,
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  ResponseEntity<Resource> importTestCasesToSuite(
      @PathVariable("suiteId") String suiteId, @RequestPart("file") MultipartFile file)
      throws IOException;
}
