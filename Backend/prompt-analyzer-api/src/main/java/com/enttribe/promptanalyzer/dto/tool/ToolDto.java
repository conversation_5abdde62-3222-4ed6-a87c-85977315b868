/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.tool;

import com.enttribe.core.generic.utils.BypassValidation;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * A comprehensive Data Transfer Object (DTO) that represents a complete tool configuration. Serves
 * as the primary tool representation including all tool-related information, configurations, and
 * associations. Author: VisionWaves Version: 1.0
 */
@Getter
@Setter
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
public class ToolDto {

  private String id;

  @NotEmpty(message = "Application name is required")
  private String applicationName;

  @NotEmpty(message = "Category is required")
  private String category;

  @NotEmpty(message = "Tool name is required")
  @Pattern(regexp = "^[a-zA-Z0-9_-]+$", message = "only a–z, 0–9, _ , and - are allowed")
  private String name;

  @NotEmpty(message = "Display name is required")
  private String displayName;
  private String version;
  private Boolean deleted = false;
  private String toolId;
  private String language;

  @NotEmpty(message = "Type is required")
  private String type;

  @BypassValidation(isScriptCheck = false)
  private String sourceCode;

  private List<SwaggerDto> swaggerApis;
  private ApiToolDto apiTool;

  @BypassValidation(isScriptCheck = false)
  @NotEmpty(message = "Description is required")
  private String description;

  @NotEmpty(message = "Class name is required")
  private String className;

  private String requestType;
  private String tags;

  @BypassValidation(isScriptCheck = false)
  private String toolJson;

  private String status;
  private ToolAuthDto toolAuthentication;

  @NotEmpty(message = "Tool image is required")
  private String toolImage;

  private Long agentId;
  private String httpMethod;
  private String parameters;
  private String url;
  private String connectorName;

  @BypassValidation(isScriptCheck = false)
  private String operationConfig;

  private Boolean returnDirect;
  private String entityName;
  private String intentName;
}
