package com.enttribe.commons.storage.gcs;

import com.enttribe.commons.storage.FileDetail;
import com.enttribe.commons.storage.StorageClient;
import com.enttribe.commons.storage.StorageOperationException;
import com.google.cloud.storage.*;

import java.io.*;
import java.net.URL;
import java.nio.channels.Channels;
import java.util.*;
import com.google.api.gax.paging.Page;


public class GcsClient implements StorageClient {

    private final Storage storage;

    public GcsClient(Storage storage) {
        this.storage = storage;
    }

    @Override
    public boolean createBucket(String bucketName) {
        try {
            storage.create(BucketInfo.of(bucketName));
            return true;
        } catch (Exception e) {
            throw new StorageOperationException("Could not create GCS bucket: " + bucketName, e);
        }
    }

    @Override
    public boolean checkBucketExist(String bucketName) {
        return storage.get(bucketName) != null;
    }

    @Override
    public List<?> listBuckets() {
        List<String> bucketNames = new ArrayList<>();
        for (Bucket bucket : storage.list().iterateAll()) {
            bucketNames.add(bucket.getName());
        }
        return bucketNames;
    }

    @Override
    public boolean uploadObject(String bucketName, String localFilePath, String objectName) {
        try {
            File file = new File(localFilePath);
            storage.create(BlobInfo.newBuilder(bucketName, objectName).build(),
                    new FileInputStream(file));
            return true;
        } catch (Exception e) {
            throw new StorageOperationException("Could not upload to GCS", e);
        }
    }

    @Override
    public void downloadObject(String bucketName, String objectName, String fileName) {
        Blob blob = storage.get(bucketName, objectName);
        if (blob == null) {
            throw new StorageOperationException("Object not found in GCS: " + objectName);
        }
        try {
            blob.downloadTo(new File(fileName).toPath());
        } catch (Exception e) {
            throw new StorageOperationException("Could not download from GCS", e);
        }
    }

    @Override
public List<FileDetail> listObjects(String bucketName) {
    List<FileDetail> files = new ArrayList<>();
    for (Blob blob : storage.list(bucketName).iterateAll()) {
        files.add(new FileDetail(
                blob.getName(),               // object name
                blob.getMetadata(),           // user metadata (may be null)
                blob.getGeneration() != null 
                        ? blob.getGeneration().toString() // versionId equivalent
                        : null
        ));
    }
    return files;
}

    

    @Override
public List<FileDetail> listObjects(String bucketName, String prefixObject) {
    List<FileDetail> files = new ArrayList<>();
    Page<Blob> blobs = storage.list(bucketName, Storage.BlobListOption.prefix(prefixObject));

    for (Blob blob : blobs.iterateAll()) {
        Map<String, String> metadata = blob.getMetadata() != null ? blob.getMetadata() : new HashMap<>();

        // GCS doesn’t have versionId like S3 → use generation number (Long) as a substitute
        String versionId = blob.getGeneration() != null ? String.valueOf(blob.getGeneration()) : null;

        files.add(new FileDetail(blob.getName(), metadata, versionId));
    }
    return files;
}


    @Override
    public InputStream getObject(String bucketName, String objectName) {
        Blob blob = storage.get(bucketName, objectName);
        if (blob == null) {
            throw new StorageOperationException("Object not found in GCS: " + objectName);
        }
        return Channels.newInputStream(blob.reader());
    }

    @Override
    public boolean putObject(String bucketName, String objectName, InputStream inputStream) {
        try {
            storage.create(BlobInfo.newBuilder(bucketName, objectName).build(),
                    inputStream.readAllBytes());
            return true;
        } catch (Exception e) {
            throw new StorageOperationException("Could not put object in GCS", e);
        }
    }

    @Override
    public boolean putObject(String bucketName, String objectName, InputStream inputStream, Map<String, String> metaData) {
        try {
            BlobInfo blobInfo = BlobInfo.newBuilder(bucketName, objectName)
                    .setMetadata(metaData)
                    .build();
            storage.create(blobInfo, inputStream.readAllBytes());
            return true;
        } catch (Exception e) {
            throw new StorageOperationException("Could not put object with metadata in GCS", e);
        }
    }

    @Override
    public void removeObject(String bucketName, String objectName) {
        try {
            storage.delete(bucketName, objectName);
        } catch (Exception e) {
            throw new StorageOperationException("Could not delete object from GCS", e);
        }
    }

    @Override
    public void removeObjectByVersion(String bucketName, String objectName, String versionId) {
        try {
            storage.delete(BlobId.of(bucketName, objectName, Long.parseLong(versionId)));
        } catch (Exception e) {
            throw new StorageOperationException("Could not delete versioned object from GCS", e);
        }
    }

    @Override
    public boolean exists(String bucketName, String objectName) {
        Blob blob = storage.get(bucketName, objectName);
        return blob != null && blob.exists();
    }

    @Override
    public boolean downloadDirectory(String bucketName, String prefix, File destinationDirectory) {
        try {
            if (!destinationDirectory.exists()) {
                destinationDirectory.mkdirs();
            }
            Iterable<Blob> blobs = storage.list(bucketName, Storage.BlobListOption.prefix(prefix)).iterateAll();
            for (Blob blob : blobs) {
                File outFile = new File(destinationDirectory, blob.getName().replace(prefix, ""));
                outFile.getParentFile().mkdirs();
                blob.downloadTo(outFile.toPath());
            }
            return true;
        } catch (Exception e) {
            throw new StorageOperationException("Could not download directory from GCS", e);
        }
    }

    @Override
    public byte[] downloadDirectoryAsZip(String bucketName, String prefix) {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             java.util.zip.ZipOutputStream zos = new java.util.zip.ZipOutputStream(baos)) {
    
            Iterable<Blob> blobs = storage.list(bucketName, Storage.BlobListOption.prefix(prefix)).iterateAll();
            for (Blob blob : blobs) {
                // Create zip entry
                zos.putNextEntry(new java.util.zip.ZipEntry(blob.getName()));
    
                // Write blob content to zip entry
                byte[] content = blob.getContent();
                zos.write(content);
    
                zos.closeEntry();
            }
            zos.finish();
            return baos.toByteArray();
        } catch (Exception e) {
            throw new StorageOperationException("Could not zip directory from GCS", e);
        }
    }
    

    @Override
    public String getPresignedObjectUrl(String bucketName, String objectName, Integer expiryTime) {
        try {
            URL url = storage.signUrl(
                    BlobInfo.newBuilder(bucketName, objectName).build(),
                    expiryTime, java.util.concurrent.TimeUnit.SECONDS);
            return url.toString();
        } catch (Exception e) {
            throw new StorageOperationException("Could not generate presigned URL for GCS", e);
        }
    }

    @Override
public Object copyObject(String srcBucketName, String srcObjectName, String desBucketName, String desObjectName) {
    try {
        CopyWriter copyWriter = storage.copy(
                Storage.CopyRequest.newBuilder()
                        .setSource(BlobId.of(srcBucketName, srcObjectName))
                        .setTarget(BlobId.of(desBucketName, desObjectName))
                        .build()
        );
        return copyWriter.getResult();
    } catch (Exception e) {
        throw new StorageOperationException("Could not copy object in GCS", e);
    }
}

}


-----------------


package com.enttribe.commons.storage;

import com.enttribe.commons.storage.s3.S3Client;
import com.enttribe.commons.storage.gcs.GcsClient;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.google.cloud.storage.Storage;
import com.google.cloud.storage.StorageOptions;

@Slf4j
public class StorageClientFactory {

    public static StorageClient create(String type) {
        System.out.println("Creating storage client for type: " + type);
        if ("s3".equalsIgnoreCase(type)) {
            return new S3Client();
        } else {
            throw new IllegalArgumentException("Unsupported storage type: " + type);
        }
    }

    public static StorageClient create(String type,Storage gcsStorage) {
        System.out.println("Creating storage client for type: " + type);
        if ("gcs".equalsIgnoreCase(type)) {
            return new GcsClient(gcsStorage);
        } else {
            throw new IllegalArgumentException("Unsupported storage type: " + type);
        }
    }
}



