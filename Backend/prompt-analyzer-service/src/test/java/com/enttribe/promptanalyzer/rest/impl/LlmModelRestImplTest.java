/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.enttribe.promptanalyzer.dto.LlmModelSdkDto;
import com.enttribe.promptanalyzer.dto.prompt.ProviderModelDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.LlmModel;
import com.enttribe.promptanalyzer.service.LlmModelService;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;

@WebMvcTest(LlmModelRestImpl.class)
class LlmModelRestImplTest {

  private static final String TEST_PROVIDER = "test-provider";
  private static final String TEST_APP_NAME = "test-app";
  private static final String TEST_TYPE = "chat";

  @Autowired private MockMvc mockMvc;

  @Autowired private ObjectMapper objectMapper;

  @MockitoBean private LlmModelService llmService;

  private LlmModel llmModel;
  private ProviderModelDto providerModelDto;
  private List<ProviderModelDto> providerModelDtos;
  private List<LlmModelSdkDto> llmModelSdkDtos;

  @BeforeEach
  void setUp() {
    // Setup LlmModel
    llmModel = new LlmModel();
    llmModel.setProvider(TEST_PROVIDER);
    llmModel.setModel("test-model");
    llmModel.setBaseUrl("https://api.test.com");
    llmModel.setApiKey("test-api-key");

    // Setup ProviderModelDto
    providerModelDto = new ProviderModelDto();
    providerModelDto.setProvider(TEST_PROVIDER);

    // Setup ProviderModelDto list
    ProviderModelDto provider1 = new ProviderModelDto();
    provider1.setProvider("provider1");
    ProviderModelDto provider2 = new ProviderModelDto();
    provider2.setProvider("provider2");
    providerModelDtos = Arrays.asList(provider1, provider2);

    // Setup LlmModelSdkDto list
    LlmModelSdkDto sdkDto1 = new LlmModelSdkDto("provider1", "https://api1.com", "key1", "model1");
    LlmModelSdkDto sdkDto2 = new LlmModelSdkDto("provider2", "https://api2.com", "key2", "model2");
    llmModelSdkDtos = Arrays.asList(sdkDto1, sdkDto2);

    // Setup success response
     Map.of("status", "success", "message", "Operation completed successfully");
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_LLMMODEL_READ"})
  @DisplayName("Get models by provider successfully")
  void testGetModelsByProviderSuccess() throws Exception {
    // Arrange
    when(llmService.getModelsByProvider(TEST_PROVIDER)).thenReturn(providerModelDto);

    // Act & Assert
    mockMvc
        .perform(get("/llm-model/getModelsByProvider").param("provider", TEST_PROVIDER))
        .andExpect(status().isOk())
        .andExpect(content().json(objectMapper.writeValueAsString(providerModelDto)));

    verify(llmService).getModelsByProvider(TEST_PROVIDER);
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_LLMMODEL_READ"})
  @DisplayName("Get models by provider with error")
  void testGetModelsByProviderError() throws Exception {
    // Arrange
    when(llmService.getModelsByProvider(anyString()))
        .thenThrow(new BusinessException("Provider not found"));

    // Act & Assert
    mockMvc
        .perform(get("/llm-model/getModelsByProvider").param("provider", "nonexistent"))
        .andExpect(status().isBadRequest());

    verify(llmService).getModelsByProvider("nonexistent");
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_LLMMODEL_READ"})
  @DisplayName("Get all providers with models successfully")
  void testGetAllProvidersWithModelsSuccess() throws Exception {
    // Arrange
    when(llmService.getAllProvidersWithModels()).thenReturn(providerModelDtos);

    // Act & Assert
    mockMvc
        .perform(get("/llm-model/all"))
        .andExpect(status().isOk())
        .andExpect(content().json(objectMapper.writeValueAsString(providerModelDtos)));

    verify(llmService).getAllProvidersWithModels();
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_LLMMODEL_READ"})
  @DisplayName("Get all providers with models returns empty list")
  void testGetAllProvidersWithModelsEmpty() throws Exception {
    // Arrange
    when(llmService.getAllProvidersWithModels()).thenReturn(Collections.emptyList());

    // Act & Assert
    mockMvc
        .perform(get("/llm-model/all"))
        .andExpect(status().isOk())
        .andExpect(content().json("[]"));

    verify(llmService).getAllProvidersWithModels();
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_LLMMODEL_READ"})
  @DisplayName("Get LLM models for SDK successfully")
  void testGetLlmModelsForSDKSuccess() throws Exception {
    // Arrange
    when(llmService.getLlmModelsForSDK(TEST_APP_NAME, "chat")).thenReturn(llmModelSdkDtos);

    // Act & Assert - Correct URL path with path variable
    mockMvc
        .perform(get("/llm-model/getLlmModelsForSDK/{appName}", TEST_APP_NAME))
        .andExpect(status().isOk())
        .andExpect(content().json(objectMapper.writeValueAsString(llmModelSdkDtos)));

    verify(llmService).getLlmModelsForSDK(TEST_APP_NAME, "chat");
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_LLMMODEL_READ"})
  @DisplayName("Get LLM models for SDK with error")
  void testGetLlmModelsForSDKError() throws Exception {
    // Arrange
    when(llmService.getLlmModelsForSDK(anyString(), anyString()))
        .thenThrow(new BusinessException("App not found"));

    // Act & Assert
    mockMvc
        .perform(get("/llm-model/getLlmModelsForSDK/{appName}", "nonexistent"))
        .andExpect(status().isBadRequest());

    verify(llmService).getLlmModelsForSDK("nonexistent", "chat");
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_LLMMODEL_READ"})
  @DisplayName("Get LLM models by type for SDK successfully")
  void testGetLlmModelsByTypeForSDKSuccess() throws Exception {
    // Arrange
    when(llmService.getLlmModelsByTypeForSDK(TEST_TYPE)).thenReturn(llmModelSdkDtos);

    // Act & Assert - Correct URL path with path variable
    mockMvc
        .perform(get("/llm-model/getLlmModelsByType/type/{type}", TEST_TYPE))
        .andExpect(status().isOk())
        .andExpect(content().json(objectMapper.writeValueAsString(llmModelSdkDtos)));

    verify(llmService).getLlmModelsByTypeForSDK(TEST_TYPE);
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_LLMMODEL_READ"})
  @DisplayName("Get LLM models by type for SDK with error")
  void testGetLlmModelsByTypeForSDKError() throws Exception {
    // Arrange
    when(llmService.getLlmModelsByTypeForSDK(anyString()))
        .thenThrow(new BusinessException("Type not found"));

    // Act & Assert
    mockMvc
        .perform(get("/llm-model/getLlmModelsByType/type/{type}", "nonexistent"))
        .andExpect(status().isBadRequest());

    verify(llmService).getLlmModelsByTypeForSDK("nonexistent");
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_LLMMODEL_READ"})
  @DisplayName("Get LLM models by type returns empty list")
  void testGetLlmModelsByTypeForSDKEmpty() throws Exception {
    // Arrange
    when(llmService.getLlmModelsByTypeForSDK(TEST_TYPE)).thenReturn(Collections.emptyList());

    // Act & Assert
    mockMvc
        .perform(get("/llm-model/getLlmModelsByType/type/{type}", TEST_TYPE))
        .andExpect(status().isOk())
        .andExpect(content().json("[]"));

    verify(llmService).getLlmModelsByTypeForSDK(TEST_TYPE);
  }
}
