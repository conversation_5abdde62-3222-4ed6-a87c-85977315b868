/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dao.LlmModelDao;
import com.enttribe.promptanalyzer.dao.PromptDao;
import com.enttribe.promptanalyzer.dto.assertion.AssertionTemplateDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptConvertorDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptDtoSdk;
import com.enttribe.promptanalyzer.dto.prompt.PromptRequestDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptVariableDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptVersionDetailsDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.LlmModel;
import com.enttribe.promptanalyzer.model.Message;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.CSVUtils;
import com.enttribe.promptanalyzer.util.JsonUtils;
import com.enttribe.promptanalyzer.util.PromptConvertor;
import com.enttribe.promptanalyzer.util.SdkUtils;
import jakarta.persistence.EntityManager;
import java.util.*;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Path;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.core.io.Resource;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class PromptServiceImplTest {

  @Mock private PromptDao promptDao;

  @Mock private CustomFilter customFilter;

  @Mock private LlmModelDao llmModelDao;

  @Mock private EntityManager entityManager;

  @Mock private Resource mockResource;

  @InjectMocks private PromptServiceImpl promptService;

  private PromptDto mockPromptDto;
  private Prompt mockPrompt;
  private LlmModel mockLlmModel;
  private PromptRequestDto mockPromptRequestDto;
  private AssertionTemplateDto mockAssertionTemplateDto;

  private MockedStatic<PromptConvertor> mockedPromptConvertor;
  private MockedStatic<SdkUtils> mockedSdkUtils;
  private MockedStatic<CSVUtils> mockedCSVUtils;
  private MockedStatic<JsonUtils> mockedJsonUtils;

  @BeforeEach
  void setUp() {
    mockPromptDto = new PromptDto();
    mockPromptDto.setApplication("TestApp");
    mockPromptDto.setName("TestPrompt");
    mockPromptDto.setCategory("TestCategory");
    mockPromptDto.setStatus("DRAFT");
    mockPromptDto.setTemperature(0.7);
    mockPromptDto.setMaxTokens(100);
    mockPromptDto.setTopP(1.0);
    mockPromptDto.setJsonMode(true);
    mockPromptDto.setLlmGuard(false);
    mockPromptDto.setType("chat");
    mockPromptDto.setTags("tag1,tag2");
    mockPromptDto.setAssertionTemplate("template");
    mockPromptDto.setDefaultFormat("format");
    mockPromptDto.setModel("gpt-4");
    mockPromptDto.setProvider("openai");
    List<Message> messages = new ArrayList<>();
    messages.add(createMessage(1L, "system", "You are a helpful assistant.", mockPrompt));
    messages.add(createMessage(2L, "user", "What is the capital of France?", mockPrompt));
    mockPromptDto.setMessages(messages);

    mockLlmModel = new LlmModel();
    mockLlmModel.setId(1);
    mockLlmModel.setModel("gpt-4");
    mockLlmModel.setProvider("openai");
    mockLlmModel.setType("chat");

    mockPrompt = new Prompt();
    mockPrompt.setId(1);
    mockPrompt.setApplication("TestApp");
    mockPrompt.setName("TestPrompt");
    mockPrompt.setCategory("TestCategory");
    mockPrompt.setStatus("DRAFT");
    mockPrompt.setVersion("v-0");
    mockPrompt.setPromptId("TestApp-TestCategory-TestPrompt-v-0");
    mockPrompt.setTemperature(0.7);
    mockPrompt.setMaxToken(100);
    mockPrompt.setTopP(1.0);
    mockPrompt.setJsonMode(true);
    mockPrompt.setLlmGuard(false);
    mockPrompt.setType("chat");
    mockPrompt.setTag("tag1,tag2");
    mockPrompt.setAssertionTemplate("template");
    mockPrompt.setDefaultFormat("format");
    mockPrompt.setLlmModel(mockLlmModel);
    mockPrompt.setMessages(messages);
    mockPrompt.setDeleted(false);
    mockPrompt.setCreatedTime(new Date());
    mockPrompt.setModifiedTime(new Date());

    mockPromptRequestDto = new PromptRequestDto();
    mockPromptRequestDto.setApplication("TestApp");
    mockPromptRequestDto.setName("TestPrompt");
    mockPromptRequestDto.setCategory("TestCategory");
    mockPromptRequestDto.setStatus("DRAFT");

    mockAssertionTemplateDto = new AssertionTemplateDto();
    mockAssertionTemplateDto.setPromptId("1");
    mockAssertionTemplateDto.setAssertionTemplate("updated template");

    // Mock static classes
    mockedPromptConvertor = mockStatic(PromptConvertor.class);
    mockedSdkUtils = mockStatic(SdkUtils.class);
    mockedCSVUtils = mockStatic(CSVUtils.class);
    mockedJsonUtils = mockStatic(JsonUtils.class);
  }

  @AfterEach
  void tearDown() {
    // Close mocked static classes
    mockedPromptConvertor.close();
    mockedSdkUtils.close();
    mockedCSVUtils.close();
    mockedJsonUtils.close();
  }

  // Test cases for savePrompt

  @Test
  @DisplayName("Save Prompt - Success")
  void testSavePromptSuccess() {
    List<PromptVersionDetailsDto> versions = new ArrayList<>();
    when(promptDao.getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString()))
        .thenReturn(versions);
    when(llmModelDao.findByModelAndProvider(anyString(), anyString(), anyString()))
        .thenReturn(mockLlmModel);
    when(promptDao.save(any(Prompt.class))).thenReturn(mockPrompt);

    Map<String, String> result = promptService.savePrompt(mockPromptDto);

    assertNotNull(result, "Prompt result should not be null");
    assertEquals(
        "success", result.get(PromptConstants.RESULT), "Prompt save result should be 'success'");
    assertEquals("1", result.get("promptId"), "Prompt ID should match expected");
    verify(promptDao, times(1))
        .getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString());
    verify(llmModelDao, times(1)).findByModelAndProvider(anyString(), anyString(), anyString());
    verify(promptDao, times(1)).save(any(Prompt.class));
  }

  @Test
  @DisplayName("Save Prompt - Constraint Violation Exception")
  void testSavePromptConstraintViolationException() {
    List<PromptVersionDetailsDto> versions = new ArrayList<>();
    when(promptDao.getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString()))
        .thenReturn(versions);
    when(llmModelDao.findByModelAndProvider(anyString(), anyString(), anyString()))
        .thenReturn(mockLlmModel);

    // Simulate a ConstraintViolationException
    ConstraintViolation<?> violation = mock(ConstraintViolation.class);
    Path mockPath = mock(Path.class);
    when(violation.getPropertyPath()).thenReturn((Path) mockPath);
    when(mockPath.toString())
        .thenReturn(
            "testField"); // Mock toString() as the assertion checks the string representation
    when(violation.getMessage()).thenReturn("test message");
    Set<ConstraintViolation<?>> violations = Set.of(violation);
    ConstraintViolationException cve = new ConstraintViolationException("test", violations);

    when(promptDao.save(any(Prompt.class))).thenThrow(cve);

    BusinessException thrown =
        assertThrows(
            BusinessException.class,
            () -> promptService.savePrompt(mockPromptDto),
            "Expected BusinessException when saving prompt fails");

    assertTrue(
        thrown.getMessage().contains("testField test message"),
        "Exception message should contain 'testField test message'");
    verify(promptDao, times(1)).save(any(Prompt.class));
  }

  @Test
  @DisplayName("Save Prompt - Generic Exception")
  void testSavePromptGenericException() {
    List<PromptVersionDetailsDto> versions = new ArrayList<>();
    when(promptDao.getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString()))
        .thenReturn(versions);
    when(llmModelDao.findByModelAndProvider(anyString(), anyString(), anyString()))
        .thenReturn(mockLlmModel);

    // Simulate a generic RuntimeException
    when(promptDao.save(any(Prompt.class))).thenThrow(new RuntimeException("Database error"));

    BusinessException thrown =
        assertThrows(
            BusinessException.class,
            () -> promptService.savePrompt(mockPromptDto),
            "Expected BusinessException when saving prompt fails");

    assertEquals(
        "Unable to save prompt", thrown.getMessage(), "Exception message should match expected");
    assertTrue(
        thrown.getCause() instanceof RuntimeException,
        "Exception cause should be RuntimeException");
    verify(promptDao, times(1)).save(any(Prompt.class));
  }

  @Test
  @DisplayName("Save Prompt - LLM Model Not Found")
  void testSavePromptLlmModelNotFound() {
    List<PromptVersionDetailsDto> versions = new ArrayList<>();
    when(promptDao.getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString()))
        .thenReturn(versions);
    when(llmModelDao.findByModelAndProvider(anyString(), anyString(), anyString()))
        .thenReturn(null); // Simulate LLM Model not found

    // Assert that an IllegalArgumentException is thrown
    BusinessException thrown =
        assertThrows(
            BusinessException.class,
            () -> promptService.savePrompt(mockPromptDto),
            "Expected BusinessException when saving prompt fails");

    // Verify the cause of the BusinessException is IllegalArgumentException
    assertTrue(
        thrown.getCause() instanceof IllegalArgumentException,
        "Exception cause should be IllegalArgumentException");

    // Verify the specific message of the caused IllegalArgumentException
    assertEquals(
        "llm model is not provided",
        thrown.getCause().getMessage(),
        "Exception message should indicate missing LLM model");

    // Verify interactions
    verify(llmModelDao, times(1)).findByModelAndProvider(anyString(), anyString(), anyString());
    verify(promptDao, never()).save(any(Prompt.class)); // Verify save is not called
  }

  @Test
  @DisplayName("Save Prompt - Existing Versions")
  void testSavePromptExistingVersions() {
    List<PromptVersionDetailsDto> versions = new ArrayList<>();
    PromptVersionDetailsDto v1 = new PromptVersionDetailsDto();
    v1.setVersion("v-1");
    versions.add(v1);
    PromptVersionDetailsDto v2 = new PromptVersionDetailsDto();
    v2.setVersion("v-2");
    versions.add(v2);

    when(promptDao.getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString()))
        .thenReturn(versions);
    when(llmModelDao.findByModelAndProvider(anyString(), anyString(), anyString()))
        .thenReturn(mockLlmModel);
    when(promptDao.save(any(Prompt.class)))
        .thenAnswer(
            invocation -> invocation.getArgument(0)); // Return the saved prompt to check version

    mockPromptDto.setStatus("PUBLISH"); // Change status to PUBLISH to trigger versioning logic

    promptService.savePrompt(mockPromptDto);

    ArgumentCaptor<Prompt> promptCaptor = ArgumentCaptor.forClass(Prompt.class);
    verify(promptDao).save(promptCaptor.capture());
    Prompt savedPrompt = promptCaptor.getValue();

    assertEquals(
        "v-3",
        savedPrompt.getVersion(),
        "Prompt version should be incremented to v-3"); // Verify the next version is correctly
    // calculated
    assertEquals(
        "TestApp-TestCategory-TestPrompt-v-3",
        savedPrompt.getPromptId(),
        "Prompt ID should match expected"); // Verify promptId
    verify(promptDao, times(1))
        .getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString());
    verify(llmModelDao, times(1)).findByModelAndProvider(anyString(), anyString(), anyString());
    verify(promptDao, times(1)).save(any(Prompt.class));
  }

  // Test cases for getVersionsOfPrompt

  @Test
  @DisplayName("Get Versions Of Prompt - Success with v-0")
  void testGetVersionsOfPromptSuccessWithV0() {
    List<PromptVersionDetailsDto> versions = new ArrayList<>();
    PromptVersionDetailsDto v0 = new PromptVersionDetailsDto();
    v0.setVersion("v-0");
    versions.add(v0);
    PromptVersionDetailsDto v1 = new PromptVersionDetailsDto();
    v1.setVersion("v-1");
    versions.add(v1);

    when(promptDao.getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString()))
        .thenReturn(versions);

    List<PromptVersionDetailsDto> result =
        promptService.getVersionsOfPrompt("app", "name", "cat", "status");

    assertNotNull(result, "Prompt result should not be null");
    assertEquals(1, result.size(), "Prompt version list size should be 1");
    assertEquals("v-1", result.get(0).getVersion(), "Prompt version should be v-1");
    verify(promptDao, times(1))
        .getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString());
  }

  @Test
  @DisplayName("Get Versions Of Prompt - No Versions")
  void testGetVersionsOfPromptNoVersions() {
    List<PromptVersionDetailsDto> versions = new ArrayList<>();

    when(promptDao.getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString()))
        .thenReturn(versions);

    List<PromptVersionDetailsDto> result =
        promptService.getVersionsOfPrompt("app", "name", "cat", "status");

    assertNotNull(result, "Prompt result should not be null");
    assertTrue(result.isEmpty(), "Result list should be empty");
    verify(promptDao, times(1))
        .getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString());
  }

  // Test cases for softDelete

  @Test
  @DisplayName("Soft Delete - Success")
  void testSoftDeleteSuccess() {
    when(promptDao.findByNanoId("3VIY6IZLEqlZKI-50rJfZ")).thenReturn(Optional.of(mockPrompt));
    when(promptDao.save(any(Prompt.class))).thenReturn(mockPrompt);

    Map<String, String> result = promptService.softDelete("1");

    assertNotNull(result, "Prompt result should not be null");
    assertEquals(
        "success",
        result.get(PromptConstants.RESULT),
        "Prompt soft delete result should be 'success'");
    assertTrue(mockPrompt.getDeleted(), "Prompt should be marked as deleted");
    verify(promptDao, times(1)).findByNanoId("1");
    verify(promptDao, times(1)).save(any(Prompt.class));
  }

  @Test
  @DisplayName("Soft Delete - Prompt Not Found")
  void testSoftDeleteNotFound() {
    when(promptDao.findByNanoId("99")).thenReturn(Optional.empty());

    ResourceNotFoundException ex =
        assertThrows(
            ResourceNotFoundException.class,
            () -> promptService.softDelete("99"),
            "Expected ResourceNotFoundException to be thrown");

    assertEquals(PromptConstants.PROMPT_NOT_FOUND, ex.getMessage(), "Error message should match");

    verify(promptDao, times(1)).findByNanoId("99");
    verify(promptDao, never()).save(any(Prompt.class));
  }

  // Test cases for getPromptById

  @Test
  @DisplayName("Get Prompt By ID - Success")
  void testGetPromptByIdSuccess() {
    String nanoId = "8NqFKMSIQHbikwSRVl6Wp";

    when(promptDao.findByNanoId(nanoId)).thenReturn(Optional.of(mockPrompt));

    // Mock the static method
    mockedPromptConvertor
        .when(() -> PromptConvertor.getPromptDto(mockPrompt))
        .thenReturn(new PromptConvertorDto());

    PromptConvertorDto result = promptService.getPromptById(nanoId);

    assertNotNull(result, "Prompt result should not be null");
    verify(promptDao, times(1)).findByNanoId(nanoId);
    mockedPromptConvertor.verify(() -> PromptConvertor.getPromptDto(mockPrompt), times(1));
  }

  @Test
  @DisplayName("Get Prompt By ID - Not Found")
  void testGetPromptByIdNotFound() {
    when(promptDao.findById(99)).thenReturn(Optional.empty());

    BusinessException thrown =
        assertThrows(
            BusinessException.class,
            () -> promptService.getPromptById("99"),
            "Expected BusinessException when getting prompt by non-existent ID");

    assertEquals(
        "Exception message should be 'Error while fetching data: No prompt found with ID 99'",
        thrown.getMessage());

    verify(promptDao, times(1)).findById(99);
    // Verify static method was not called
    mockedPromptConvertor.verify(() -> PromptConvertor.getPromptDto(any(Prompt.class)), never());
  }

  @Test
  @DisplayName("Get Prompt By ID - Null ID")
  void testGetPromptByIdNullId() {
    IllegalArgumentException thrown =
        assertThrows(
            IllegalArgumentException.class,
            () -> promptService.getPromptById(null),
            "Expected IllegalArgumentException when getting prompt by null ID");

    assertEquals(
        "Exception message should be 'ID is required.'", thrown.getMessage());
    verify(promptDao, never()).findById(anyInt());
  }

  // Test cases for findPromptById (SDK)

  @Test
  @DisplayName("Find Prompt By ID (SDK) - Success")
  void testFindPromptByIdSdkSuccess() {
    when(promptDao.findById(1)).thenReturn(Optional.of(mockPrompt));
    // Mock the static method call
    mockedSdkUtils
        .when(() -> SdkUtils.getPromptDto(any(Prompt.class)))
        .thenReturn(new PromptDtoSdk()); // Return a dummy DTO

    PromptDtoSdk result = promptService.findPromptById(1);

    assertNotNull(result, "Prompt result should not be null");
    verify(promptDao, times(1)).findById(1);
    mockedSdkUtils.verify(() -> SdkUtils.getPromptDto(eq(mockPrompt)), times(1));
  }

  @Test
  @DisplayName("Find Prompt By ID (SDK) - Not Found")
  void testFindPromptByIdSdkNotFound() {
    when(promptDao.findById(99)).thenReturn(Optional.empty());

    BusinessException thrown =
        assertThrows(
            BusinessException.class,
            () -> promptService.findPromptById(99),
            "Expected BusinessException when finding prompt by non-existent ID");

    assertEquals(
        "Exception message should be 'Prompt with ID 99 not found.'",
        thrown.getMessage());
    verify(promptDao, times(1)).findById(99);
    // Verify static method was not called
    mockedSdkUtils.verify(() -> SdkUtils.getPromptDto(any(Prompt.class)), never());
  }

  // Test cases for exists

  @Test
  @DisplayName("Exists Prompt - True")
  void testExistsPromptTrue() {
    when(promptDao.existsByNameAndApplicationAndCategoryAndStatus(
            anyString(), anyString(), anyString(), anyString()))
        .thenReturn(true);

    Map<String, Object> result = promptService.exists(mockPromptRequestDto);

    assertNotNull(result, "Prompt result should not be null");
    assertTrue((Boolean) result.get(PromptConstants.RESULT), "Prompt result should be true");
    verify(promptDao, times(1))
        .existsByNameAndApplicationAndCategoryAndStatus(
            "TestPrompt", "TestApp", "TestCategory", "DRAFT");
  }

  @Test
  @DisplayName("Exists Prompt - False")
  void testExistsPromptFalse() {
    when(promptDao.existsByNameAndApplicationAndCategoryAndStatus(
            anyString(), anyString(), anyString(), anyString()))
        .thenReturn(false);

    Map<String, Object> result = promptService.exists(mockPromptRequestDto);

    assertNotNull(result, "Prompt result should not be null");
    assertFalse((Boolean) result.get(PromptConstants.RESULT), "Prompt result should be false");
    verify(promptDao, times(1))
        .existsByNameAndApplicationAndCategoryAndStatus(
            "TestPrompt", "TestApp", "TestCategory", "DRAFT");
  }

  @Test
  @DisplayName("Exists Prompt - Exception")
  void testExistsPromptException() {
    when(promptDao.existsByNameAndApplicationAndCategoryAndStatus(
            anyString(), anyString(), anyString(), anyString()))
        .thenThrow(new RuntimeException("DB error"));

    BusinessException thrown =
        assertThrows(
            BusinessException.class,
            () -> promptService.exists(mockPromptRequestDto),
            "Expected BusinessException when checking prompt existence fails");

    assertEquals(
        "Exception message should be 'An error occurred while processing the existence check.'",
        thrown.getMessage());
    assertInstanceOf(
        RuntimeException.class, thrown.getCause(), "Exception cause should be RuntimeException");
    verify(promptDao, times(1))
        .existsByNameAndApplicationAndCategoryAndStatus(
            "TestPrompt", "TestApp", "TestCategory", "DRAFT");
  }

  // Test cases for updateAssertionTemplate

  @Test
  @DisplayName("Update Assertion Template - Success")
  void testUpdateAssertionTemplateSuccess() {
    // Setup
    String nanoId = "8NqFKMSIQHbikwSRVl6Wp";
    mockPrompt.setNanoId(nanoId);
    mockPrompt.setStatus("PUBLISH"); // Ensure status is PUBLISH

    AssertionTemplateDto dto = new AssertionTemplateDto();
    dto.setPromptId(nanoId);
    dto.setAssertionTemplate("updated template");

    when(promptDao.findByNanoId(nanoId)).thenReturn(Optional.of(mockPrompt));
    when(promptDao.save(any(Prompt.class))).thenReturn(mockPrompt);

    // Call the method
    Map<String, String> result = promptService.updateAssertionTemplate(dto);

    // Assertions
    assertNotNull(result, "Prompt result should not be null");
    assertEquals("success", result.get(PromptConstants.RESULT), "Result should be 'success'");
    assertEquals(
        "updated template", mockPrompt.getAssertionTemplate(), "Template should be updated");

    // Verifications
    verify(promptDao, times(1)).findByNanoId(nanoId);
    verify(promptDao, times(1)).save(mockPrompt);
  }

  @Test
  @DisplayName("Update Assertion Template - Prompt Not Found")
  void testUpdateAssertionTemplateNotFound() {
    // Mock findById to return empty for the ID in the DTO
    when(promptDao.findByNanoId(mockAssertionTemplateDto.getPromptId()))
        .thenReturn(Optional.empty());

    // Assert that a BusinessException is thrown when calling the service method
    ResourceNotFoundException thrown =
        assertThrows(
            ResourceNotFoundException.class,
            () -> promptService.updateAssertionTemplate(mockAssertionTemplateDto),
            "Expected ResourceNotFoundException when updating assertion template for non-existent prompt");

    // Assert the specific exception message
    assertEquals("Prompt not found", thrown.getMessage());

    // Verify that findById was called exactly once with the correct ID from the DTO
    verify(promptDao, times(1)).findByNanoId(mockAssertionTemplateDto.getPromptId());

    // Verify that save was never called
    verify(promptDao, never()).save(any(Prompt.class));
  }

  @Test
  @DisplayName("Update Assertion Template - Status Not PUBLISH")
  void testUpdateAssertionTemplateStatusNotPublish() {
    mockPrompt.setStatus("DRAFT"); // Status is not PUBLISH
    when(promptDao.findById(1)).thenReturn(Optional.of(mockPrompt));

    BusinessException thrown =
        assertThrows(
            BusinessException.class,
            () -> promptService.updateAssertionTemplate(mockAssertionTemplateDto),
            "Expected BusinessException when updating assertion template with non-PUBLISH status");

    assertEquals(
        "Prompt status is not PUBLISH. Cannot update assertionTemplate.", thrown.getMessage());
    verify(promptDao, times(1)).findById(1);
    verify(promptDao, never()).save(any(Prompt.class));
  }

  // Test cases for updatePrompt

  @Test
  @DisplayName("Update Prompt - Success (Status DRAFT)")
  void testUpdatePromptSuccessStatusDraft() {
    mockPromptDto.setId("8NqFKMSIQHbikwSRVl6Wp"); // nanoId
    mockPromptDto.setStatus("DRAFT");
    mockPrompt.setStatus("DRAFT");

    when(promptDao.findByNanoId("8NqFKMSIQHbikwSRVl6Wp")).thenReturn(Optional.of(mockPrompt));
    when(llmModelDao.findByModelAndProvider(anyString(), anyString(), anyString()))
        .thenReturn(mockLlmModel);
    when(promptDao.save(any(Prompt.class))).thenReturn(mockPrompt);

    Map<String, String> result = promptService.updatePrompt(mockPromptDto);

    assertNotNull(result, "Prompt result should not be null");
    assertEquals(
        "success", result.get(PromptConstants.RESULT), "Prompt update result should be 'success'");
    assertEquals("DRAFT", mockPrompt.getStatus(), "Prompt status should remain DRAFT");

    verify(promptDao, times(1)).findByNanoId("8NqFKMSIQHbikwSRVl6Wp");
    verify(llmModelDao, times(1)).findByModelAndProvider(anyString(), anyString(), anyString());
    verify(promptDao, times(1)).save(any(Prompt.class));
    verify(promptDao, never())
        .getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString());
  }

  @Test
  @DisplayName("Update Prompt - Success (Status DRAFT to PUBLISH)")
  void testUpdatePromptSuccessStatusDraftToPublish() {
    // Setup test data
    mockPromptDto.setId("8NqFKMSIQHbikwSRVl6Wp");
    mockPromptDto.setStatus("PUBLISH");
    mockPromptDto.setApplication("TestApp");
    mockPromptDto.setCategory("TestCategory");
    mockPromptDto.setName("TestPrompt");

    mockPrompt.setStatus("DRAFT");

    List<PromptVersionDetailsDto> existingVersions = new ArrayList<>();
    PromptVersionDetailsDto v1 = new PromptVersionDetailsDto();
    v1.setVersion("v-1");
    existingVersions.add(v1);

    // Mock behavior
    when(promptDao.findByNanoId("8NqFKMSIQHbikwSRVl6Wp")).thenReturn(Optional.of(mockPrompt));
    when(promptDao.getVersionsOfPrompt("TestApp", "TestPrompt", "TestCategory", "PUBLISH"))
        .thenReturn(existingVersions);
    when(llmModelDao.findByModelAndProvider(anyString(), anyString(), anyString()))
        .thenReturn(mockLlmModel);
    when(promptDao.save(any(Prompt.class))).thenAnswer(invocation -> invocation.getArgument(0));

    // Execute
    Map<String, String> result = promptService.updatePrompt(mockPromptDto);

    // Verify result
    assertNotNull(result, "Prompt result should not be null");
    assertEquals("success", result.get(PromptConstants.RESULT));
    assertEquals("PUBLISH", mockPrompt.getStatus());
    assertEquals("v-2", mockPrompt.getVersion());
    assertEquals("TestApp-TestCategory-TestPrompt-v-2", mockPrompt.getPromptId());

    // Verify method calls
    verify(promptDao, times(1)).findByNanoId("8NqFKMSIQHbikwSRVl6Wp");
    verify(promptDao, times(1))
        .getVersionsOfPrompt("TestApp", "TestPrompt", "TestCategory", "PUBLISH");
    verify(llmModelDao, times(1)).findByModelAndProvider(anyString(), anyString(), anyString());
    verify(promptDao, times(1)).save(any(Prompt.class));
  }

  @Test
  @DisplayName("Update Prompt - Prompt Not Found")
  void testUpdatePromptNotFound() {
    mockPromptDto.setId("99");
    when(promptDao.findById(99)).thenReturn(Optional.empty());

    BusinessException thrown =
        assertThrows(
            BusinessException.class,
            () -> promptService.updatePrompt(mockPromptDto),
            "Expected BusinessException when updating prompt fails");

    assertEquals("Unable to update prompt", thrown.getMessage());
    verify(promptDao, times(1)).findByNanoId(mockPromptDto.getId());
  }

  @Test
  @DisplayName("Update Prompt - Constraint Violation Exception")
  void testUpdatePromptConstraintViolationException() {
    mockPromptDto.setId("1");
    when(promptDao.findById(1)).thenReturn(Optional.of(mockPrompt));
    when(llmModelDao.findByModelAndProvider(anyString(), anyString(), anyString()))
        .thenReturn(mockLlmModel);

    // Simulate a ConstraintViolationException
    ConstraintViolation<?> violation = mock(ConstraintViolation.class);
    Path mockPath = mock(Path.class);
    when(violation.getPropertyPath()).thenReturn((Path) mockPath);
    when(mockPath.toString())
        .thenReturn(
            "testField"); // Mock toString() as the assertion checks the string representation
    when(violation.getMessage()).thenReturn("test message");
    Set<ConstraintViolation<?>> violations = Set.of(violation);
    ConstraintViolationException cve = new ConstraintViolationException("test", violations);

    when(promptDao.save(any(Prompt.class))).thenThrow(cve);

    BusinessException thrown =
        assertThrows(
            BusinessException.class,
            () -> promptService.updatePrompt(mockPromptDto),
            "Expected BusinessException when updating prompt fails");

    assertTrue(
        thrown.getMessage().contains("testField test message"),
        "Exception message should contain 'testField test message'");
    verify(promptDao, times(1)).save(any(Prompt.class));
  }

  @Test
  @DisplayName("Update Prompt - Generic Exception")
  void testUpdatePromptGenericException() {
    mockPromptDto.setId("1");
    when(promptDao.findById(1)).thenReturn(Optional.of(mockPrompt));
    when(llmModelDao.findByModelAndProvider(anyString(), anyString(), anyString()))
        .thenReturn(mockLlmModel);

    // Simulate a generic RuntimeException
    when(promptDao.save(any(Prompt.class))).thenThrow(new RuntimeException("Database error"));

    BusinessException thrown =
        assertThrows(
            BusinessException.class,
            () -> promptService.updatePrompt(mockPromptDto),
            "Expected BusinessException when updating prompt fails");

    assertEquals("Unable to update prompt", thrown.getMessage());
    assertTrue(
        thrown.getCause() instanceof RuntimeException,
        "Exception cause should be RuntimeException");
    verify(promptDao, times(1)).save(any(Prompt.class));
  }

  // Test cases for search

  @Test
  @DisplayName("Search Prompts - Success")
  void testSearchPromptsSuccess() {
    List<Prompt> mockPrompts = List.of(mockPrompt);
    when(customFilter.searchByFilter(
            eq(Prompt.class), anyString(), anyString(), anyString(), anyInt(), anyInt()))
        .thenReturn(mockPrompts);
    // Mock the static method call
    mockedPromptConvertor
        .when(() -> PromptConvertor.getPromptDtoList(anyList()))
        .thenReturn(List.of(new PromptConvertorDto())); // Return a dummy list

    List<PromptConvertorDto> result = promptService.search("filter", 0, 10, "name", "asc");

    assertNotNull(result, "Prompt result should not be null");
    assertFalse(result.isEmpty(), "Prompt search result should not be empty");
    verify(customFilter, times(1)).searchByFilter(Prompt.class, "filter", "name", "asc", 0, 10);
    mockedPromptConvertor.verify(() -> PromptConvertor.getPromptDtoList(eq(mockPrompts)), times(1));
  }

  @Test
  @DisplayName("Search Prompts - No Results")
  void testSearchPromptsNoResults() {
    List<Prompt> mockPrompts = new ArrayList<>();
    when(customFilter.searchByFilter(
            eq(Prompt.class), anyString(), anyString(), anyString(), anyInt(), anyInt()))
        .thenReturn(mockPrompts);
    // Mock the static method call
    mockedPromptConvertor
        .when(() -> PromptConvertor.getPromptDtoList(anyList()))
        .thenReturn(new ArrayList<>()); // Return a dummy empty list

    List<PromptConvertorDto> result = promptService.search("filter", 0, 10, "name", "asc");

    assertNotNull(result, "Prompt result should not be null");
    assertTrue(result.isEmpty(), "Prompt search result should be empty");
    verify(customFilter, times(1)).searchByFilter(Prompt.class, "filter", "name", "asc", 0, 10);
    mockedPromptConvertor.verify(() -> PromptConvertor.getPromptDtoList(eq(mockPrompts)), times(1));
  }

  // Test cases for count

  @Test
  @DisplayName("Count Prompts - Success")
  void testCountPromptsSuccess() {
    when(customFilter.countByFilter(eq(Prompt.class), anyString())).thenReturn(10L);

    Long result = promptService.count("filter");

    assertEquals(10L, result, "Prompt count should match expected");
    verify(customFilter, times(1)).countByFilter(Prompt.class, "filter");
  }

  // Test cases for getPromptByApplication

  @Test
  @DisplayName("Get Prompt By Application - Success")
  void testGetPromptByApplicationSuccess() {
    List<Prompt> mockPrompts = List.of(mockPrompt);
    when(promptDao.getPromptByApplication(anyString())).thenReturn(mockPrompts);
    // Mock the static method call
    mockedSdkUtils
        .when(() -> SdkUtils.getPromptDtoList(anyList()))
        .thenReturn(List.of(new PromptDtoSdk())); // Return a dummy list

    List<PromptDtoSdk> result = promptService.getPromptByApplication("TestApp");

    assertNotNull(result, "Prompt result should not be null");
    assertFalse(result.isEmpty(), "Prompt get by application result should not be empty");
    verify(promptDao, times(1)).getPromptByApplication("TestApp");
    mockedSdkUtils.verify(() -> SdkUtils.getPromptDtoList(eq(mockPrompts)), times(1));
  }

  @Test
  @DisplayName("Get Prompt By Application - Not Found")
  void testGetPromptByApplicationNotFound() {
    List<Prompt> mockPrompts = new ArrayList<>();
    when(promptDao.getPromptByApplication(anyString())).thenReturn(mockPrompts);
    // Mock the static method call
    mockedSdkUtils
        .when(() -> SdkUtils.getPromptDtoList(anyList()))
        .thenReturn(new ArrayList<>()); // Return a dummy empty list

    List<PromptDtoSdk> result = promptService.getPromptByApplication("NonExistentApp");

    assertNotNull(result, "Prompt result should not be null");
    assertTrue(result.isEmpty(), "Prompt get by application result should be empty");
    verify(promptDao, times(1)).getPromptByApplication("NonExistentApp");
    mockedSdkUtils.verify(() -> SdkUtils.getPromptDtoList(eq(mockPrompts)), times(1));
  }

  @Test
  @DisplayName("Get Prompt By Application - Null App Name")
  void testGetPromptByApplicationNullAppName() {
    IllegalArgumentException thrown =
        assertThrows(
            IllegalArgumentException.class,
            () -> promptService.getPromptByApplication(null),
            "Expected IllegalArgumentException for null application name");

    assertEquals("Application name cannot be null or empty.", thrown.getMessage());
    verify(promptDao, never()).getPromptByApplication(anyString());
  }

  @Test
  @DisplayName("Get Prompt By Application - Empty App Name")
  void testGetPromptByApplicationEmptyAppName() {
    IllegalArgumentException thrown =
        assertThrows(
            IllegalArgumentException.class,
            () -> promptService.getPromptByApplication(""),
            "Expected IllegalArgumentException for empty application name");

    assertEquals("Application name cannot be null or empty.", thrown.getMessage());
    verify(promptDao, never()).getPromptByApplication(anyString());
  }

  // Test cases for getDistinctApplications

  @Test
  @DisplayName("Get Distinct Applications - Success")
  void testGetDistinctApplicationsSuccess() {
    List<String> expectedApps = Arrays.asList("App1", "App2", "App3");
    when(promptDao.getDistinctApplications(anyString())).thenReturn(expectedApps);

    List<String> result = promptService.getDistinctApplications("test");

    assertNotNull(result, "Prompt result should not be null");
    assertEquals(expectedApps, result, "Distinct applications should match expected");
    verify(promptDao, times(1)).getDistinctApplications("test");
  }

  @Test
  @DisplayName("Get Distinct Categories By App - Success")
  void testGetDistinctCategoriesByAppSuccess() {
    List<String> expectedCategories = Arrays.asList("Cat1", "Cat2", "Cat3");
    when(promptDao.getDistinctCategoriesByApp(anyString())).thenReturn(expectedCategories);

    List<String> result = promptService.getDistinctCategoriesByApp("TestApp");

    assertNotNull(result, "Prompt result should not be null");
    assertEquals(expectedCategories, result, "Distinct categories should match expected");
    verify(promptDao, times(1)).getDistinctCategoriesByApp("TestApp");
  }

  @Test
  @DisplayName("Get Prompt Basic Detail By Application - Success")
  void testGetPromptBasicDetailByApplicationSuccess() {
    List<Object[]> mockResults = new ArrayList<>();
    Object[] result1 = new Object[] {"TestPrompt", "test-id-1"};
    Object[] result2 = new Object[] {"TestPrompt2", "test-id-2"};
    mockResults.add(result1);
    mockResults.add(result2);

    when(promptDao.getPromptBasicDetailByApplication(anyString())).thenReturn(mockResults);

    List<Map<String, String>> result = promptService.getPromptBasicDetailByApplication("TestApp");

    assertNotNull(result, "Prompt result should not be null");
    assertEquals(2, result.size(), "Prompt basic detail list size should be 2");
    assertEquals("TestPrompt", result.get(0).get("name"), "First prompt name should match");
    assertEquals("test-id-1", result.get(0).get("promptId"), "First prompt ID should match");
    assertEquals("TestPrompt2", result.get(1).get("name"), "Second prompt name should match");
    assertEquals("test-id-2", result.get(1).get("promptId"), "Second prompt ID should match");
    verify(promptDao, times(1)).getPromptBasicDetailByApplication("TestApp");
  }

  @Test
  @DisplayName("Fetch Prompt By Id - Success")
  void testFetchPromptByIdSuccess() {
    String nanoId = "8NqFKMSIQHbikwSRVl6Wp";

    // Mock the DAO to return a valid prompt for the nano ID
    when(promptDao.findByNanoId(nanoId)).thenReturn(Optional.of(mockPrompt));

    // Mock the static method
    mockedPromptConvertor
        .when(() -> PromptConvertor.mapToPromptDto(mockPrompt))
        .thenReturn(new PromptDto());

    // Call the service
    PromptDto result = promptService.fetchPromptById(nanoId);

    // Assertions
    assertNotNull(result, "Prompt result should not be null");

    // Verify the mocks
    verify(promptDao, times(1)).findByNanoId(nanoId);
    mockedPromptConvertor.verify(() -> PromptConvertor.mapToPromptDto(mockPrompt), times(1));
  }

  @Test
  @DisplayName("Fetch Prompt By Id - Not Found")
  void testFetchPromptByIdNotFound() {
    when(promptDao.findById(anyInt())).thenReturn(Optional.empty());

    BusinessException thrown =
        assertThrows(
            BusinessException.class,
            () -> promptService.fetchPromptById("1"),
            "Expected BusinessException when fetching prompt by ID fails");

    assertEquals(
        "Exception message should be 'Prompt not found with ID: 1'",
        thrown.getMessage());
    verify(promptDao, times(1)).findByNanoId("1");
  }

  @Test
  @DisplayName("Get Prompt By Name - Success")
  void testGetPromptByNameSuccess() {
    when(promptDao.findByName(anyString())).thenReturn(mockPrompt);
    mockedPromptConvertor
        .when(() -> PromptConvertor.mapToPromptDto(any(Prompt.class)))
        .thenReturn(new PromptDto());

    PromptDto result = promptService.getPromptByName("TestPrompt");

    assertNotNull(result, "Prompt result should not be null");
    verify(promptDao, times(1)).findByName("TestPrompt");
    mockedPromptConvertor.verify(() -> PromptConvertor.mapToPromptDto(eq(mockPrompt)), times(1));
  }

  @Test
  @DisplayName("Get Prompt By Name - Not Found")
  void testGetPromptByNameNotFound() {
    when(promptDao.findByName(anyString())).thenReturn(null);

    BusinessException thrown =
        assertThrows(
            BusinessException.class,
            () -> promptService.getPromptByName("NonExistentPrompt"),
            "Expected BusinessException for non-existent prompt name");

    assertEquals(
        "Exception message should be 'Prompt not found with name: NonExistentPrompt'",
        thrown.getMessage());
    verify(promptDao, times(1)).findByName("NonExistentPrompt");
  }

  @Test
  @DisplayName("Get Prompt Variables By App Name - Success")
  void testGetPromptVariablesByAppNameSuccess() {
    String appName = "TestApp";
    List<Prompt> prompts = Arrays.asList(mockPrompt);
    when(promptDao.getPromptByApplication(appName)).thenReturn(prompts);

    List<PromptVariableDto> result = promptService.getPromptVariablesByAppName(appName);

    assertNotNull(result, "Prompt result should not be null");
    assertFalse(result.isEmpty(), "Prompt variables by app name result should not be empty");
    verify(promptDao).getPromptByApplication(appName);
  }

  @Test
  @DisplayName("Get Prompt Variables By App Name - No Prompts")
  void testGetPromptVariablesByAppNameNoPrompts() {
    String appName = "TestApp";
    when(promptDao.getPromptByApplication(appName)).thenReturn(new ArrayList<>());

    List<PromptVariableDto> result = promptService.getPromptVariablesByAppName(appName);

    assertNotNull(result, "Prompt result should not be null");
    assertTrue(result.isEmpty(), "Prompt variables by app name result should be empty");
    verify(promptDao).getPromptByApplication(appName);
  }

  @Test
  @DisplayName("Get Prompt Variables By Id - Success")
  void testGetPromptVariablesByIdSuccess() {
    String promptId = "TEST-001";
    when(promptDao.findByPromptId(promptId)).thenReturn(mockPrompt);

    PromptVariableDto result = promptService.getPromptVariablesById(promptId);

    assertNotNull(result, "Prompt result should not be null");
    verify(promptDao).findByPromptId(promptId);
  }

  @Test
  @DisplayName("Get Prompt Variables By Id - Not Found")
  void testGetPromptVariablesByIdNotFound() {
    String promptId = "NON-EXISTENT";
    when(promptDao.findByPromptId(promptId)).thenReturn(null);

    BusinessException thrown =
        assertThrows(
            BusinessException.class,
            () -> promptService.getPromptVariablesById(promptId),
            "Expected BusinessException when getting prompt variables by ID fails");

    assertEquals(
        "Exception message should be 'Prompt ID " + promptId + " not found'",
        "Prompt ID " + promptId + " not found",
        thrown.getMessage());
    verify(promptDao).findByPromptId(promptId);
  }

  @Test
  @DisplayName("Update Tag By Id - Success")
  void testUpdateTagByIdSuccess() {
    String id = "1";
    Map<String, String> tags = new HashMap<>();
    tags.put("key1", "value1");
    tags.put("key2", "value2");

    when(promptDao.findByNanoId(id)).thenReturn(Optional.of(mockPrompt));
    when(promptDao.save(any(Prompt.class))).thenReturn(mockPrompt);

    Map<String, String> result = promptService.updateTagById(id, tags);

    assertNotNull(result, "Prompt result should not be null");
    assertEquals(
        "success",
        result.get(PromptConstants.RESULT),
        "Prompt update tag result should be 'success'");
    verify(promptDao).findByNanoId(id);
    verify(promptDao).save(any(Prompt.class));
  }

  @Test
  @DisplayName("Update Tag By Id - Prompt Not Found")
  void testUpdateTagByIdNotFound() {
    String id = "1";
    Map<String, String> tags = new HashMap<>();
    tags.put("key1", "value1");

    when(promptDao.findByNanoId(id)).thenReturn(Optional.empty());

    BusinessException thrown =
        assertThrows(
            BusinessException.class,
            () -> promptService.updateTagById(id, tags),
            "Expected BusinessException when updating tag by ID fails");

    assertEquals(
        "Exception message should be 'prompt is not found for id : " + id + "'",
        "prompt is not found for id : " + id,
        thrown.getMessage());
    verify(promptDao).findByNanoId(id);
    verify(promptDao, never()).save(any(Prompt.class));
  }

  // Helper method to create Message objects for tests
  private Message createMessage(Long id, String role, String content, Prompt prompt) {
    Message message = new Message();
    message.setId(id);
    message.setRole(role);
    message.setContent(content);
    message.setPrompt(prompt);
    return message;
  }
}
