/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.enttribe.product.security.spring.userdetails.CustomerInfo;
import com.enttribe.promptanalyzer.config.McpToolService;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dao.PromptDao;
import com.enttribe.promptanalyzer.dao.ToolDao;
import com.enttribe.promptanalyzer.dto.tool.*;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.Tool;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.AESUtils;
import com.enttribe.promptanalyzer.util.TemplateUtils;
import java.util.*;
import java.util.Collections;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class ToolServiceImplTest {

  @Mock private ToolDao toolDao;

  @Mock private PromptDao promptDao;

  @Mock private CustomFilter customFilter;

  @Mock private McpToolService mcpToolService;

  @Mock private CustomerInfo customerInfo;

  @Spy @InjectMocks private ToolServiceImpl toolService;

  private ToolDto toolDto;
  private Tool existingTool;
  private static final String TEST_NANO_ID = "4ZgHLWRnKl26guKpIZQ52";

  @BeforeEach
  void setUp() {
    // Initialize test data
    toolDto = new ToolDto();
    toolDto.setId(TEST_NANO_ID);
    toolDto.setName("TestTool");
    toolDto.setApplicationName("TestApp");
    toolDto.setCategory("TestCategory");
    toolDto.setVersion("1.0");
    toolDto.setType("API");
    toolDto.setDescription("Test Description");
    toolDto.setDisplayName("Test Tool");
    toolDto.setLanguage("Java");
    toolDto.setTags("test,tag");
    toolDto.setToolImage("test.jpg");
    toolDto.setReturnDirect(false);
    toolDto.setStatus("DRAFT");

    ApiToolDto apiToolDto = new ApiToolDto();
    apiToolDto.setMethod("GET");
    apiToolDto.setUrl("http://test.com");
    apiToolDto.setRequestBody("{}");
    apiToolDto.setHeaders("{}");
    toolDto.setApiTool(apiToolDto);

    existingTool = new Tool();
    existingTool.setId(1);
    existingTool.setNanoId(TEST_NANO_ID);
    existingTool.setToolName("TestTool");
    existingTool.setApplicationName("TestApp");
    existingTool.setCategory("TestCategory");
    existingTool.setVersion("1.0");
    existingTool.setType("API");
    existingTool.setDescription("Test Description");
    existingTool.setDisplayName("Test Tool");
    existingTool.setLanguage("Java");
    existingTool.setTags("test,tag");
    existingTool.setToolImage("test.jpg");
    existingTool.setReturnDirect(false);
    existingTool.setToolId("TestApp_TestTool_TestCategory_1.0");
    existingTool.setStatus("DRAFT");

    // Set up resources
    ReflectionTestUtils.setField(
        toolService, "apiTool", new ClassPathResource("template/api_tool.st"));
    ReflectionTestUtils.setField(
        toolService, "connectorTool", new ClassPathResource("template/connector_tool.st"));
  }

  @Test
  void testCreateToolSuccess() {
    // Mock the versions list
    List<ToolVersionDetailsDto> versions = new ArrayList<>();
    when(toolDao.getVersionsOfTool(anyString(), anyString(), anyString(), anyString()))
        .thenReturn(versions);
    when(toolDao.save(any(Tool.class))).thenReturn(existingTool);

    Map<String, String> result = toolService.createTool(toolDto);

    assertNotNull(result, "Tool result should not be null");
    assertEquals(
        "success", result.get(PromptConstants.RESULT), "Tool save result should be 'success'");
    verify(toolDao).save(any(Tool.class));
  }

  @Test
  void testCreateToolDuplicateTool() {
    // Mock the versions list
    List<ToolVersionDetailsDto> versions = new ArrayList<>();
    when(toolDao.getVersionsOfTool(anyString(), anyString(), anyString(), anyString()))
        .thenReturn(versions);

    // Mock the save to throw a BusinessException
    when(toolDao.save(any(Tool.class)))
        .thenThrow(
            new BusinessException("Tool with name " + toolDto.getName() + " already exists"));

    BusinessException exception =
        assertThrows(
            BusinessException.class,
            () -> {
              toolService.createTool(toolDto);
            });

    assertTrue(
        exception.getMessage().contains("already exists"),
        "Exception message should contain 'already exists'");
    verify(toolDao).save(any(Tool.class));
  }

  @Test
  void testUpdateToolSuccess() {
    when(toolDao.findByNanoId(anyString())).thenReturn(Optional.of(existingTool));
    when(toolDao.save(any(Tool.class))).thenReturn(existingTool);

    Map<String, String> result = toolService.updateTool(toolDto);

    assertNotNull(result, "Tool result should not be null");
    assertEquals(
        "success", result.get(PromptConstants.RESULT), "Tool update result should be 'success'");
    verify(toolDao).save(any(Tool.class));
  }

  @Test
  void testUpdateToolNotFound() {
    // Mock findByNanoId to return empty
    when(toolDao.findByNanoId(anyString())).thenReturn(Optional.empty());

    // Expect ResourceNotFoundException to be thrown
    assertThrows(
        ResourceNotFoundException.class,
        () -> {
          toolService.updateTool(toolDto);
        },
        "Expected ResourceNotFoundException when updating tool not found");

    // Verify that save was never called
    verify(toolDao, never()).save(any(Tool.class));
  }

  @Test
  void testChangeToolStatusSuccess() {
    when(toolDao.findByNanoId(anyString())).thenReturn(Optional.of(existingTool));
    when(toolDao.save(any(Tool.class))).thenReturn(existingTool);

    Map<String, String> result = toolService.changeToolStatus(TEST_NANO_ID, "ACTIVE");

    assertNotNull(result, "Tool result should not be null");
    assertEquals(
        "success",
        result.get(PromptConstants.RESULT),
        "Tool status update result should be 'success'");
  }

  @Test
  void testGetToolByIdSuccess() {
    when(toolDao.findByNanoId(anyString())).thenReturn(Optional.of(existingTool));

    ToolConvertorDto result = toolService.getToolById(TEST_NANO_ID);

    assertNotNull(result, "Tool result should not be null");
    assertEquals(existingTool.getToolName(), result.getName(), "Tool name should match expected");
  }

  @Test
  void testGetToolsByIdsSuccess() {
    List<Tool> tools = Arrays.asList(existingTool);
    when(toolDao.findAllById(anyList())).thenReturn(tools);

    List<ToolDtoSdk> result = toolService.getToolsByIds(Arrays.asList(1));

    assertNotNull(result, "Tool result should not be null");
    assertFalse(result.isEmpty(), "Tool list should not be empty");
    assertEquals(1, result.size(), "Tool list size should match expected");
  }

  @Test
  void testCheckCompilationSuccess() {
    String sourceCode = "public class TestClass { public String test() { return \"test\"; } }";
    String className = "TestClass";
    String encryptedSourceCode = AESUtils.encryptString(sourceCode);

    Map<String, Object> map = toolService.checkCompilation(encryptedSourceCode, className);
    boolean result = (boolean) map.get(PromptConstants.RESULT);

    assertTrue(result, "Tool compilation should be successful");
  }

  @Test
  void testExportToolSuccess() {
    List<Tool> tools = Arrays.asList(existingTool);
    when(toolDao.getToolsByApplication(anyString())).thenReturn(tools);

    ResponseEntity<Resource> result = toolService.exportTool("TestApp");

    assertNotNull(result, "Tool result should not be null");
    assertTrue(
        result.getStatusCode().is2xxSuccessful(), "Tool export status should be 2xx successful");
  }

  @Test
  void testSoftDeleteSuccess() {
    existingTool.setDeleted(false);
    when(toolDao.findByNanoId(TEST_NANO_ID)).thenReturn(Optional.of(existingTool));
    when(toolDao.save(any(Tool.class))).thenReturn(existingTool);
    when(customerInfo.getUserId()).thenReturn(100);
    // Use a mock for getCreator() if it's a method, otherwise set the field directly
    existingTool.setCreator(100);
    doNothing().when(customFilter).verifyCreator(100, 100);
    ReflectionTestUtils.setField(toolService, "customerInfo", customerInfo);
    ReflectionTestUtils.setField(toolService, "customFilter", customFilter);
    Map<String, String> result = toolService.softDelete(TEST_NANO_ID);
    assertNotNull(result, "Tool result should not be null");
    assertEquals(
        "success", result.get(PromptConstants.RESULT), "Tool delete result should be 'success'");
    verify(toolDao).save(any(Tool.class));
  }

  @Test
  void testSoftDeleteNotFound() {
    when(toolDao.findByNanoId(anyString())).thenReturn(Optional.empty());

    Map<String, String> result = toolService.softDelete(TEST_NANO_ID);

    assertNotNull(result, "Tool result should not be null");
    assertEquals(
        "failed", result.get(PromptConstants.RESULT), "Tool operation result should be 'failed'");
    verify(toolDao, never()).save(any(Tool.class));
  }

  @Test
  void testUpdateTagByIdSuccess() {
    when(toolDao.findByNanoId(anyString())).thenReturn(Optional.of(existingTool));
    when(toolDao.save(any(Tool.class))).thenReturn(existingTool);

    Map<String, String> tags = new HashMap<>();
    tags.put("tags", "new,tags");

    Map<String, String> result = toolService.updateTagById(TEST_NANO_ID, tags);

    assertNotNull(result, "Tool result should not be null");
    assertEquals(
        "success",
        result.get(PromptConstants.RESULT),
        "Tool status update result should be 'success'");
  }

  @Test
  void testGetToolByNameSuccess() {
    when(toolDao.findByToolName(anyString())).thenReturn(existingTool);

    ToolDto result = toolService.getToolByName("TestTool");

    assertNotNull(result, "Tool result should not be null");
    assertEquals(existingTool.getToolName(), result.getName(), "Tool name should match expected");
  }

  @Test
  void testGetToolByNameNotFound() {
    when(toolDao.findByToolName(anyString())).thenReturn(null);

    assertThrows(
        ResourceNotFoundException.class,
        () -> {
          toolService.getToolByName("NonExistentTool");
        },
        "Expected ResourceNotFoundException when getting tool by name that does not exist");
  }

  @Test
  @DisplayName("Test createTool with connector type")
  void testCreateToolWithConnectorType() {
    String connector = "public class ${name} { private String field1 = \"${field1}\"; }";
    toolDto.setType("CONNECTOR");
    toolDto.setOperationConfig("{\"field1\":\"${value1}\",\"field2\":\"${value2}\"}");
    toolDto.setConnectorName("TestConnector");
    toolDto.setDescription("Test Description");
    toolDto.setApplicationName("TestApp");
    toolDto.setCategory("TestCategory");
    toolDto.setName("TestConnector");
    toolDto.setDisplayName("Test Connector");
    toolDto.setLanguage("Java");
    toolDto.setStatus("DRAFT");

    existingTool.setType("CONNECTOR");
    existingTool.setClassName("com.enttribe.promptanalyzer.function.ConnectorTool");

    when(toolDao.getVersionsOfTool(anyString(), anyString(), anyString(), anyString()))
        .thenReturn(Collections.emptyList());
    when(toolDao.save(any(Tool.class))).thenReturn(existingTool);

    // Mock static TemplateUtils and AESUtils
    try (MockedStatic<TemplateUtils> mockedTemplateUtils = mockStatic(TemplateUtils.class);
        MockedStatic<AESUtils> mockedAesUtils = mockStatic(AESUtils.class)) {
      String dummySourceCode = "public class ConnectorTool {}";
      mockedTemplateUtils
          .when(() -> TemplateUtils.getResolvedPrompt(eq(connector), anyMap()))
          .thenReturn(dummySourceCode);
      Map<String, byte[]> dummyByteCode = new HashMap<>();
      dummyByteCode.put("ConnectorTool.class", new byte[0]);
      // Use spy to mock compileSourceCode
      doReturn(dummyByteCode).when(toolService).compileSourceCode(anyString(), anyString());
      mockedAesUtils
          .when(() -> AESUtils.encryptString(anyString()))
          .thenReturn("encryptedSourceCode");

      Map<String, String> result = toolService.createTool(toolDto);
      assertNotNull(result, "Tool result should not be null");
      assertEquals("success", result.get(PromptConstants.RESULT), "Result should be 'success'");
      verify(toolDao).save(any(Tool.class));
    }
  }

  @Test
  void testUpdateToolWithConnectorType() {
    toolDto.setId("1Iw5XnrZ2ARYkAsgGXy0y");
    toolDto.setType("CONNECTOR");
    toolDto.setOperationConfig("{\"field1\":\"${value1}\",\"field2\":\"${value2}\"}");
    toolDto.setConnectorName("TestConnector");
    toolDto.setDescription("Test Description");
    toolDto.setApplicationName("TestApp");
    toolDto.setCategory("TestCategory");
    toolDto.setName("TestConnector");
    toolDto.setDisplayName("Test Connector");
    toolDto.setLanguage("Java");
    toolDto.setStatus("DRAFT");

    existingTool.setClassName("com.enttribe.promptanalyzer.function.TestConnector");
    existingTool.setType("CONNECTOR");
    existingTool.setToolName("TestConnector");
    existingTool.setDisplayName("Test Connector");
    existingTool.setDescription("Test Description");
    existingTool.setApplicationName("TestApp");
    existingTool.setCategory("TestCategory");
    existingTool.setLanguage("Java");
    existingTool.setStatus("DRAFT");

    when(toolDao.findByNanoId("1Iw5XnrZ2ARYkAsgGXy0y")).thenReturn(Optional.of(existingTool));

    try (MockedStatic<TemplateUtils> templateUtilsMocked = Mockito.mockStatic(TemplateUtils.class);
        MockedStatic<AESUtils> aesUtilsMocked = Mockito.mockStatic(AESUtils.class)) {
      templateUtilsMocked
          .when(() -> TemplateUtils.getResolvedPrompt(eq("connectorTool"), anyMap()))
          .thenReturn("Generated Source Code");
      Map<String, byte[]> byteCodeMap = new HashMap<>();
      byteCodeMap.put("ConnectorTool.class", new byte[] {0x01, 0x02});
      aesUtilsMocked
          .when(() -> AESUtils.encryptString("Generated Source Code"))
          .thenReturn("encrypted-source");
      doReturn(byteCodeMap)
          .when(toolService)
          .compileSourceCode(
              ("Generated Source Code"),
              (PromptConstants.COM_ENTTRIBE_PROMPTANALYZER_FUNCTION_CONNECTORTOOL));
      when(toolDao.save(any())).thenReturn(existingTool);
      Map<String, String> result = toolService.updateTool(toolDto);
      assertNotNull(result);
      assertEquals("success", result.get(PromptConstants.RESULT));
      verify(toolDao).save(any(Tool.class));
    }
  }

  @Test
  void testUpdateToolWithSourceCodeType() {
    // Arrange
    toolDto.setType("sourceCode");
    // The source code needs to be encrypted for sourceCode type tools
    String sourceCode = "public class TestClass { public String test() { return \"test\"; } }";
    toolDto.setSourceCode(AESUtils.encryptString(sourceCode));
    toolDto.setClassName("com.enttribe.promptanalyzer.function.TestClass");
    toolDto.setDescription("Test Description");
    toolDto.setApplicationName("TestApp");
    toolDto.setCategory("TestCategory");
    toolDto.setName("TestClass");
    toolDto.setDisplayName("Test Class");
    toolDto.setLanguage("Java");
    toolDto.setStatus("DRAFT");

    existingTool.setClassName("com.enttribe.promptanalyzer.function.TestClass");
    existingTool.setType("sourceCode");
    existingTool.setToolName("TestClass");
    existingTool.setDisplayName("Test Class");
    existingTool.setDescription("Test Description");
    existingTool.setApplicationName("TestApp");
    existingTool.setCategory("TestCategory");
    existingTool.setLanguage("Java");
    existingTool.setStatus("DRAFT");

    when(toolDao.findByNanoId(anyString())).thenReturn(Optional.of(existingTool));
    when(toolDao.save(any(Tool.class))).thenReturn(existingTool);

    // Act
    Map<String, String> result = toolService.updateTool(toolDto);

    // Assert
    assertNotNull(result, "Tool result should not be null");
    assertEquals(
        "success", result.get(PromptConstants.RESULT), "Tool update result should be 'success'");
    verify(toolDao).save(any(Tool.class));
  }

  @Test
  void testGenerateToolsFromWorkflowSuccess() {
    // Arrange
    ToolWorkflowDto workflowDto = new ToolWorkflowDto();
    workflowDto.setName("TestWorkflow");
    workflowDto.setDisplayName("Test Workflow");
    workflowDto.setDescription("Test Description");
    workflowDto.setHostName("http://test.com");
    workflowDto.setStatus("DRAFT");
    workflowDto.setCategory("TestCategory");
    workflowDto.setApplicationName("TestApp");
    workflowDto.setToolImage("test.jpg");

    ToolAuthDto authDto = new ToolAuthDto();
    authDto.setAuthType("NONE");
    workflowDto.setToolAuthentication(authDto);

    List<Map<String, String>> requiredParams = new ArrayList<>();
    Map<String, String> param = new HashMap<>();
    param.put("name", "testParam");
    param.put("type", "String");
    requiredParams.add(param);
    workflowDto.setRequiredParameters(requiredParams);

    when(toolDao.save(any(Tool.class))).thenReturn(existingTool);

    // Act
    toolService.generateToolsFromWorkflow(workflowDto);

    // Assert
    verify(toolDao).save(any(Tool.class));
  }

  @Test
  void testCheckCompilationWithInvalidCode() {
    // Arrange
    String sourceCode = "public class TestClass { invalid code }";
    String className = "TestClass";

    // Act
    Map<String, Object> map = toolService.checkCompilation(sourceCode, className);
    boolean result = (boolean) map.get(PromptConstants.RESULT);
    // Assert
    assertFalse(result, "Tool compilation should fail for invalid code");
  }

  @Test
  void testExistsToolWhenToolExists() {
    // Arrange
    when(toolDao.existsByToolName(anyString())).thenReturn(true);

    // Act
    Map<String, Boolean> result = toolService.existsTool("TestTool");

    // Assert
    assertNotNull(result, "Tool result should not be null");
    assertTrue(result.get(PromptConstants.RESULT), "Tool should exist in the system");
  }

  @Test
  void testExistsToolWhenToolDoesNotExist() {
    // Arrange
    when(toolDao.existsByToolName(anyString())).thenReturn(false);

    // Act
    Map<String, Boolean> result = toolService.existsTool("NonExistentTool");

    // Assert
    assertNotNull(result, "Tool result should not be null");
    assertFalse(result.get(PromptConstants.RESULT), "Tool should not exist in the system");
  }

  @Test
  void testGetToolsByIdsV1Success() {
    // Arrange
    List<Tool> tools = Arrays.asList(existingTool);
    when(toolDao.findByNanoIds(anyList())).thenReturn(tools);

    // Act
    List<ToolConvertorDto> result = toolService.getToolsByIdsV1(Arrays.asList(TEST_NANO_ID));

    // Assert
    assertNotNull(result, "Tool result should not be null");
    assertFalse(result.isEmpty(), "Tool list should not be empty");
    assertEquals(1, result.size(), "Tool list size should match expected");
  }

  @Test
  void testSearchSuccess() {
    // Arrange
    List<Tool> tools = Arrays.asList(existingTool);
    when(customFilter.searchByFilter(Tool.class, "TestTool", "id", "asc", 0, 10)).thenReturn(tools);

    // Act
    List<ToolConvertorDto> result = toolService.search("TestTool", 0, 10, "id", "asc");

    // Assert
    assertNotNull(result, "Tool result should not be null");
    assertEquals(1, result.size(), "Tool list size should match expected");
    assertEquals(
        existingTool.getToolId(), result.get(0).getToolId(), "Tool ID should match expected");
  }

  @Test
  void testCountSuccess() {
    // Arrange
    when(customFilter.countByFilter(Tool.class, "TestTool")).thenReturn(1L);

    // Act
    Long result = toolService.count("TestTool");

    // Assert
    assertNotNull(result, "Tool result should not be null");
    assertEquals(1L, result, "Tool count should match expected");
  }
}
