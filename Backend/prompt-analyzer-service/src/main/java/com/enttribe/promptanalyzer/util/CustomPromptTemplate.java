/*
 * Copyright (c) 2024 Vwaves Technologies Private Limited
 *
 * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.
 * Product: Aspose.Total for Java
 * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations
 * Order ID: 250610115636
 *
 * This source code is part of a proprietary software system and may not be
 * distributed, modified, or reused without explicit written permission from
 * Vwaves Technologies Private Limited.
 *
 * Contact: <EMAIL>
 */
package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.exception.BusinessException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.antlr.runtime.Token;
import org.antlr.runtime.TokenStream;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplateActions;
import org.springframework.ai.chat.prompt.PromptTemplateMessageActions;
import org.springframework.ai.content.Media;
import org.springframework.core.io.Resource;
import org.springframework.util.StreamUtils;
import org.stringtemplate.v4.ST;

/**
 * Custom implementation of prompt template using StringTemplate for dynamic template rendering.
 *
 * <p>This class provides functionality to create and render prompt templates with dynamic
 * variable substitution using StringTemplate library. It implements both PromptTemplateActions
 * and PromptTemplateMessageActions interfaces to provide comprehensive prompt template functionality.</p>
 *
 * <p>Features:</p>
 * <ul>
 *   <li>Template rendering with variable substitution</li>
 *   <li>Support for both string and resource-based templates</li>
 *   <li>Dynamic model management</li>
 *   <li>Message and prompt creation</li>
 *   <li>Input variable extraction and validation</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 */
public class CustomPromptTemplate implements PromptTemplateActions, PromptTemplateMessageActions {
  protected String template;
  private ST st;
  private Map<String, Object> dynamicModel;

  private static final int LBRACK = 23;
  private static final int IDENT = 25;
  private static final int RBRACK = 13;
  private static final int COMMA = 24;
  private static final int TOKEN_LOOKAHEAD_OFFSET = 2;

  /**
   * Creates a CustomPromptTemplate from a resource file.
   *
   * @param resource the resource containing the template
   * @throws BusinessException if the resource cannot be read
   * @throws IllegalArgumentException if the template is not valid
   */
  public CustomPromptTemplate(Resource resource) {
    this.dynamicModel = new HashMap<>();

    try (InputStream inputStream = resource.getInputStream()) {
      this.template = StreamUtils.copyToString(inputStream, Charset.defaultCharset());
    } catch (IOException ex) {
      throw new BusinessException(PromptConstants.FAILED_TO_READ_RESOURCE, ex);
    }

    try {
      this.st = new ST(this.template, '{', '}');
    } catch (Exception ex) {
      throw new IllegalArgumentException(PromptConstants.TEMPLATE_NOT_VALID, ex);
    }
  }

  /**
   * Creates a CustomPromptTemplate from a string template.
   *
   * @param template the template string
   * @throws IllegalArgumentException if the template is not valid
   */
  public CustomPromptTemplate(String template) {
    this.dynamicModel = new HashMap<>();
    this.template = template;

    try {
      this.st = new ST(this.template, '{', '}');
    } catch (Exception ex) {
      throw new IllegalArgumentException(PromptConstants.TEMPLATE_NOT_VALID, ex);
    }
  }

  /**
   * Creates a CustomPromptTemplate from a string template with initial model data.
   *
   * @param template the template string
   * @param model the initial model data
   * @throws IllegalArgumentException if the template is not valid
   */
  public CustomPromptTemplate(String template, Map<String, Object> model) {
    this.dynamicModel = new HashMap<>();
    this.template = template;

    try {
      this.st = new ST(this.template, '{', '}');

      for (Map.Entry<String, Object> entry : model.entrySet()) {
        this.add(entry.getKey(), entry.getValue());
      }

    } catch (Exception ex) {
      throw new IllegalArgumentException(PromptConstants.TEMPLATE_NOT_VALID, ex);
    }
  }

  public CustomPromptTemplate(Resource resource, Map<String, Object> model) {
    this.dynamicModel = new HashMap<>();

    try (InputStream inputStream = resource.getInputStream()) {
      this.template = StreamUtils.copyToString(inputStream, Charset.defaultCharset());
    } catch (IOException ex) {
      throw new BusinessException(PromptConstants.FAILED_TO_READ_RESOURCE, ex);
    }

    try {
      this.st = new ST(this.template, '{', '}');

      for (Map.Entry<String, Object> entry : model.entrySet()) {
        this.add(entry.getKey(), entry.getValue());
      }

    } catch (Exception ex) {
      throw new IllegalArgumentException(PromptConstants.TEMPLATE_NOT_VALID, ex);
    }
  }

  public void add(String name, Object value) {
    this.st.add(name, value);
    this.dynamicModel.put(name, value);
  }

  public String getTemplate() {
    return this.template;
  }

  public String render() {
    this.validate(this.dynamicModel);
    return this.st.render();
  }

  public String render(Map<String, Object> model) {
    this.validate(model);

    for (Map.Entry<String, Object> entry : model.entrySet()) {
      String key = entry.getKey();
      Object value = entry.getValue();

      if (this.st.getAttribute(key) != null) {
        this.st.remove(key);
      }

      if (value instanceof Resource resource) {
        this.st.add(key, this.renderResource(resource));
      } else {
        this.st.add(key, value);
      }
    }

    return this.st.render();
  }

  private String renderResource(Resource resource) {
    try {
      return resource.getContentAsString(Charset.defaultCharset());
    } catch (IOException e) {
      throw new BusinessException(PromptConstants.FAILED_TO_READ_RESOURCE, e);
    }
  }

  public Message createMessage() {
    return new UserMessage(this.render());
  }

  @Override
  public Message createMessage(List<Media> mediaList) {
    return null;
  }

  public Message createMessage(Map<String, Object> model) {
    return new UserMessage(this.render(model));
  }

  public Prompt create() {
    return new Prompt(this.render(new HashMap<>()));
  }

  public Prompt create(ChatOptions modelOptions) {
    return new Prompt(this.render(new HashMap<>()), modelOptions);
  }

  public Prompt create(Map<String, Object> model) {
    return new Prompt(this.render(model));
  }

  public Prompt create(Map<String, Object> model, ChatOptions modelOptions) {
    return new Prompt(this.render(model), modelOptions);
  }

  public Set<String> getInputVariables() {
    TokenStream tokens = this.st.impl.tokens;
    Set<String> inputVariables = new HashSet<>();
    boolean isInsideList = false;

    for (int i = 0; i < tokens.size(); ++i) {
      Token token = tokens.get(i);
      if (token.getType() == LBRACK
          && i + 1 < tokens.size()
          && tokens.get(i + 1).getType() == IDENT) {
        if (i + TOKEN_LOOKAHEAD_OFFSET < tokens.size()
            && tokens.get(i + TOKEN_LOOKAHEAD_OFFSET).getType() == RBRACK) {
          inputVariables.add(tokens.get(i + 1).getText());
          isInsideList = true;
        }
      } else if (token.getType() == COMMA) {
        isInsideList = false;
      } else if (!isInsideList && token.getType() == IDENT) {
        inputVariables.add(token.getText());
      }
    }

    return inputVariables;
  }

  private Set<String> getModelKeys(Map<String, Object> model) {
    Set<String> dynamicVariableNames = new HashSet<>(this.dynamicModel.keySet());
    Set<String> modelVariables = new HashSet<>(model.keySet());
    modelVariables.addAll(dynamicVariableNames);
    return modelVariables;
  }

  protected void validate(Map<String, Object> model) {
    Set<String> templateTokens = this.getInputVariables();
    Set<String> modelKeys = this.getModelKeys(model);
    if (!modelKeys.containsAll(templateTokens)) {
      templateTokens.removeAll(modelKeys);
      throw new IllegalStateException(
          "Not all template variables were replaced. Missing variable names are " + templateTokens);
    }
  }
}
