/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.dto.tool.ToolVersionDetailsDto;
import com.enttribe.promptanalyzer.model.Tool;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Data Access Object interface for Tool entity operations. Provides methods to interact with the
 * Tool table in the database.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public interface ToolDao extends JpaRepository<Tool, Integer> {

  /**
   * Retrieves version details of tools matching the specified criteria. Only returns non-deleted
   * tools.
   *
   * @param applicationName The name of the application
   * @param name The name of the tool
   * @param category The category of the tool
   * @param status The status of the tool
   * @return List of ToolVersionDetailsDto containing tool IDs and versions
   */
  @Query(
      "SELECT new com.enttribe.promptanalyzer.dto.tool.ToolVersionDetailsDto(t.id, t.version) FROM Tool t WHERE t.applicationName = :applicationName AND t.toolName = :name AND t.category = :category AND t.status = :status AND t.deleted = false")
  List<ToolVersionDetailsDto> getVersionsOfTool(
      String applicationName, String name, String category, String status);

  /**
   * Retrieves all published tools for a specific application. Only returns non-deleted tools with
   * 'PUBLISH' status.
   *
   * @param applicationName The name of the application
   * @return List of Tool entities
   */
  @Query(
      "SELECT t FROM Tool t WHERE t.applicationName = :applicationName AND t.deleted = false AND t.status = 'PUBLISH'")
  List<Tool> getToolsByApplication(@Param("applicationName") String applicationName);

  /**
   * Retrieves all tools associated with a specific agent.
   *
   * @param agentId The ID of the agent
   * @return List of Tool entities
   */
  @Query("SELECT t FROM Tool t where t.agentId= :agentId")
  List<Tool> getToolByAgentId(Long agentId);

  /**
   * Finds a non-deleted tool by its name.
   *
   * @param toolName The name of the tool to find
   * @return Tool entity if found, null otherwise
   */
  @Query("SELECT t FROM Tool t WHERE t.toolName = :toolName AND t.deleted = false")
  Tool findByToolName(String toolName);

  /**
   * Checks if a tool exists by its name.
   *
   * @param toolName The name of the tool to check
   * @return true if the tool exists, false otherwise
   */
  @Query(
      "SELECT CASE WHEN COUNT(t) > 0 THEN TRUE ELSE FALSE END FROM Tool t WHERE t.toolName = :toolName")
  boolean existsByToolName(String toolName);

  /**
   * Retrieves a list of all tool names that are not deleted.
   *
   * @return a list of tool names
   */
  @Query("SELECT t.toolName FROM Tool t WHERE t.deleted = false")
  List<String> getListOfToolName();

  /**
   * Counts the number of tools for a specific application that are not deleted.
   *
   * @param appName the application name to count tools for
   * @return the count of tools for the application
   */
  @Query("SELECT COUNT(t) FROM Tool t WHERE t.applicationName = :appName and t.deleted = false")
  Long countToolByAppName(String appName);

  /**
   * Retrieves a list of tool names for a specific application that are not deleted.
   *
   * @param appName the application name to get tool names for
   * @return a list of tool names for the application
   */
  @Query("SELECT t.toolName FROM Tool t WHERE t.applicationName = :appName and t.deleted = false")
  List<String> getListOfToolNameByAppName(String appName);

  /**
   * Finds a tool by its nanoId that is not deleted.
   *
   * @param nanoId the nanoId to search for
   * @return an Optional containing the tool if found and not deleted, empty otherwise
   */
  @Query("SELECT t FROM Tool t WHERE t.nanoId = :nanoId and t.deleted = false")
  Optional<Tool> findByNanoId(String nanoId);

  /**
   * Finds all tools by their type that are not deleted.
   *
   * @param type the type to search for
   * @return a list of tools matching the type that are not deleted
   */
  @Query("SELECT t FROM Tool t WHERE t.type = :type and t.deleted = false")
  List<Tool> findByType(String type);

  /**
   * Finds all tools by their nanoIds that are not deleted.
   *
   * @param nanoIds the list of nanoIds to search for
   * @return a list of tools matching the provided nanoIds that are not deleted
   */
  @Query("SELECT t FROM Tool t WHERE t.nanoId IN :nanoIds and t.deleted = false")
  List<Tool> findByNanoIds(List<String> nanoIds);

  /**
   * Finds the first batch of tools with null nanoId for pagination.
   *
   * @param pageable the pagination parameters
   * @return a list of tools with null nanoId
   */
  @Query("SELECT t FROM Tool t WHERE t.nanoId IS NULL")
  List<Tool> findFirstBatchWithNullNanoId(Pageable pageable);
}
