/*
 * Copyright (c) 2024 Vwaves Technologies Private Limited
 *
 * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.
 * Product: Aspose.Total for Java
 * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations
 * Order ID: 250610115636
 *
 * This source code is part of a proprietary software system and may not be
 * distributed, modified, or reused without explicit written permission from
 * Vwaves Technologies Private Limited.
 *
 * Contact: <EMAIL>
 */
package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.result.AssertionResultResponseDto;
import com.enttribe.promptanalyzer.dto.result.TestCaseResultResponseDto;
import com.enttribe.promptanalyzer.dto.result.TestResultResponseDto;
import java.util.List;

/**
 * Service interface for managing test result operations and data retrieval.
 * 
 * <p>This interface provides comprehensive functionality for searching, retrieving, and counting
 * various types of test results including assertion results, test case results, and general test results.
 * It supports pagination, filtering, and sorting capabilities for efficient data management.</p>
 *
 * <p>Key responsibilities:</p>
 * <ul>
 *   <li>Search and retrieve assertion results with filtering and pagination</li>
 *   <li>Search and retrieve test case results with advanced querying</li>
 *   <li>Search and retrieve general test results</li>
 *   <li>Count results for each result type with filtering support</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface ResultService {

  /**
   * Searches for assertion results with pagination, filtering, and sorting capabilities.
   * 
   * <p>This method retrieves assertion results based on the provided filter criteria,
   * with support for pagination and customizable sorting options.</p>
   *
   * @param filter optional filter criteria for searching assertion results
   * @param offset the pagination offset (zero-based)
   * @param size the number of results to return per page
   * @param orderBy optional field name to sort by
   * @param orderType optional sort direction ("asc" or "desc")
   * @return a list of assertion result response DTOs matching the criteria
   */
  List<AssertionResultResponseDto> searchAssertionResults(
      String filter, Integer offset, Integer size, String orderBy, String orderType);

  /**
   * Counts the total number of assertion results matching the filter criteria.
   * 
   * @param filter optional filter criteria for counting assertion results
   * @return the total count of matching assertion results
   */
  Long countAssertionResults(String filter);

  /**
   * Searches for test case results with pagination, filtering, and sorting capabilities.
   * 
   * <p>This method retrieves test case results based on the provided filter criteria,
   * with support for pagination and customizable sorting options.</p>
   *
   * @param filter optional filter criteria for searching test case results
   * @param offset the pagination offset (zero-based)
   * @param size the number of results to return per page
   * @param orderBy optional field name to sort by
   * @param orderType optional sort direction ("asc" or "desc")
   * @return a list of test case result response DTOs matching the criteria
   */
  List<TestCaseResultResponseDto> searchTestCaseResults(
      String filter, Integer offset, Integer size, String orderBy, String orderType);

  /**
   * Counts the total number of test case results matching the filter criteria.
   * 
   * @param filter optional filter criteria for counting test case results
   * @return the total count of matching test case results
   */
  Long countTestCaseResults(String filter);

  /**
   * Searches for general test results with pagination, filtering, and sorting capabilities.
   * 
   * <p>This method retrieves general test results based on the provided filter criteria,
   * with support for pagination and customizable sorting options.</p>
   *
   * @param filter optional filter criteria for searching test results
   * @param offset the pagination offset (zero-based)
   * @param size the number of results to return per page
   * @param orderBy optional field name to sort by
   * @param orderType optional sort direction ("asc" or "desc")
   * @return a list of test result response DTOs matching the criteria
   */
  List<TestResultResponseDto> searchTestResults(
      String filter, Integer offset, Integer size, String orderBy, String orderType);

  /**
   * Counts the total number of test results matching the filter criteria.
   * 
   * @param filter optional filter criteria for counting test results
   * @return the total count of matching test results
   */
  Long countTestResults(String filter);
}
