/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.enttribe.promptanalyzer.enums.AssertionType;
import com.enttribe.promptanalyzer.enums.MatchStrategy;
import com.enttribe.promptanalyzer.model.AgentTestCase;
import com.enttribe.promptanalyzer.model.AgentTestCaseAssertion;
import com.enttribe.promptanalyzer.model.Assertion;
import com.enttribe.promptanalyzer.model.AssertionResult;
import com.enttribe.promptanalyzer.model.TestCaseResult;
import com.enttribe.promptanalyzer.util.test_case.ToolCallAssertions;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.function.Function;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class TestCaseAssertionProcessor {

  private final Function<LlmEvalRequest, TestExecutionServiceV1Impl.LlmEval> llmEvaluator;

  public List<AssertionResult> processAssertions(
      AgentTestCase agentTestCase,
      String llmResponse,
      String userQuestion,
      TestCaseResult testCaseResult) {
    List<AssertionResult> assertionResults = new ArrayList<>();
    boolean toolInvoked = ToolCallAssertions.isToolInvoked(llmResponse);

    log.debug("toolInvoked : {}", toolInvoked);

    for (AgentTestCaseAssertion agentTestCaseAssertion :
        agentTestCase.getAgentTestCaseAssertions()) {
      AssertionResult result =
          processAssertion(
              agentTestCaseAssertion, llmResponse, userQuestion, toolInvoked, testCaseResult);
      assertionResults.add(result);
    }

    return assertionResults;
  }

  private AssertionResult processAssertion(
      AgentTestCaseAssertion agentTestCaseAssertion,
      String llmResponse,
      String userQuestion,
      boolean toolInvoked,
      TestCaseResult testCaseResult) {
    Assertion assertion = agentTestCaseAssertion.getAssertion();
    AssertionType assertionType = assertion.getAssertionType();

    AssertionResult result =
        switch (assertionType) {
          case TOOL_PARAMETERS -> processToolParametersAssertion(
              agentTestCaseAssertion, llmResponse, userQuestion, toolInvoked, assertion);
          case TOOL_CALLED -> processToolCalledAssertion(
              agentTestCaseAssertion, llmResponse, userQuestion, toolInvoked, assertion);
          case LLM_RESPONSE -> processLlmResponseAssertion(
              agentTestCaseAssertion, llmResponse, userQuestion);
        };

    result.setTestCaseResult(testCaseResult);
    result.setNanoId(NanoIdUtils.randomNanoId());
    return result;
  }

  private AssertionResult processToolParametersAssertion(
      AgentTestCaseAssertion agentTestCaseAssertion,
      String llmResponse,
      String userQuestion,
      boolean toolInvoked,
      Assertion assertion) {
    String expectedValue = agentTestCaseAssertion.getExpectedValue();
    String parameterName = agentTestCaseAssertion.getParameterName();

    log.debug("llmResponse before in tool parameter : {}", llmResponse);
    llmResponse = ToolCallAssertions.getParameterValue(llmResponse, parameterName);
    log.debug("llmResponse after in tool parameter : {}", llmResponse);

    if (!toolInvoked) {
      return createToolNotInvokedResult(
          expectedValue,
          parameterName,
          llmResponse,
          userQuestion,
          AssertionType.TOOL_PARAMETERS,
          assertion.getMatchStrategy());
    }

    try {
      if (assertion.getMatchStrategy().equals(MatchStrategy.EVALUATION)) {
        return processEvaluationAssertion(llmResponse, userQuestion, expectedValue, parameterName);
      } else {
        return processJsonArgumentAssertion(
            llmResponse, expectedValue, parameterName, assertion.getMatchStrategy(), userQuestion);
      }
    } catch (Exception e) {
      return createErrorResult(
          e,
          expectedValue,
          parameterName,
          llmResponse,
          userQuestion,
          AssertionType.TOOL_PARAMETERS,
          assertion.getMatchStrategy());
    }
  }

  private AssertionResult processToolCalledAssertion(
      AgentTestCaseAssertion agentTestCaseAssertion,
      String llmResponse,
      String userQuestion,
      boolean toolInvoked,
      Assertion assertion) {
    String expectedValue = agentTestCaseAssertion.getExpectedValue();

    if (!toolInvoked) {
      return AssertionResult.builder()
          .actual(llmResponse)
          .expected(expectedValue)
          .isPassed(false)
          .userQuestion(userQuestion)
          .assertionType(AssertionType.TOOL_CALLED.toString())
          .matchStrategy(assertion.getMatchStrategy().toString())
          .comment("Tool is not invoked")
          .accuracyScore(0.0)
          .createdTime(new Date())
          .build();
    }

    try {
      return ToolCallAssertions.assertToolName(llmResponse, expectedValue, userQuestion);
    } catch (Exception e) {
      log.error("error in asserting tool name : {}", e.getMessage());
      String toolName = ToolCallAssertions.getToolName(llmResponse);
      return AssertionResult.builder()
          .actual(toolName)
          .expected(expectedValue)
          .isPassed(false)
          .userQuestion(userQuestion)
          .assertionType(AssertionType.TOOL_CALLED.toString())
          .matchStrategy(assertion.getMatchStrategy().toString())
          .comment(e.getMessage())
          .accuracyScore(0.0)
          .createdTime(new Date())
          .build();
    }
  }

  private AssertionResult processLlmResponseAssertion(
      AgentTestCaseAssertion agentTestCaseAssertion, String llmResponse, String userQuestion) {
    String expectedValue = agentTestCaseAssertion.getExpectedValue();
    LlmEvalRequest request = new LlmEvalRequest(llmResponse, userQuestion, expectedValue);
    TestExecutionServiceV1Impl.LlmEval llmEval = llmEvaluator.apply(request);

    return AssertionResult.builder()
        .actual(llmResponse)
        .expected(expectedValue)
        .isPassed(llmEval.isPassed())
        .userQuestion(userQuestion)
        .assertionType(AssertionType.LLM_RESPONSE.toString())
        .matchStrategy(MatchStrategy.EVALUATION.toString())
        .comment(llmEval.reason())
        .accuracyScore(llmEval.accuracyScore())
        .createdTime(new Date())
        .build();
  }

  private AssertionResult processEvaluationAssertion(
      String llmResponse, String userQuestion, String expectedValue, String parameterName) {
    LlmEvalRequest request = new LlmEvalRequest(llmResponse, userQuestion, expectedValue);
    TestExecutionServiceV1Impl.LlmEval llmEval = llmEvaluator.apply(request);

    return AssertionResult.builder()
        .actual(llmResponse)
        .expected(expectedValue)
        .parameterName(parameterName)
        .isPassed(llmEval.isPassed())
        .userQuestion(userQuestion)
        .assertionType(AssertionType.TOOL_PARAMETERS.toString())
        .matchStrategy(MatchStrategy.EVALUATION.toString())
        .comment(llmEval.reason())
        .accuracyScore(llmEval.accuracyScore())
        .createdTime(new Date())
        .build();
  }

  private AssertionResult processJsonArgumentAssertion(
      String llmResponse,
      String expectedValue,
      String parameterName,
      MatchStrategy matchStrategy,
      String userQuestion) {

    return ToolCallAssertions.assertJsonArgument(
        llmResponse, parameterName, expectedValue, matchStrategy, userQuestion);
  }

  private AssertionResult createToolNotInvokedResult(
      String expectedValue,
      String parameterName,
      String llmResponse,
      String userQuestion,
      AssertionType assertionType,
      MatchStrategy matchStrategy) {

    return AssertionResult.builder()
        .actual(llmResponse)
        .expected(expectedValue)
        .parameterName(parameterName)
        .isPassed(false)
        .userQuestion(userQuestion)
        .assertionType(assertionType.toString())
        .matchStrategy(matchStrategy.toString())
        .comment("Tool is not invoked")
        .accuracyScore(0.0)
        .createdTime(new Date())
        .build();
  }

  private AssertionResult createErrorResult(
      Exception e,
      String expectedValue,
      String parameterName,
      String llmResponse,
      String userQuestion,
      AssertionType assertionType,
      MatchStrategy matchStrategy) {

    log.error("error in asserting : {}", e.getMessage());
    String toolName = ToolCallAssertions.getToolName(llmResponse);

    return AssertionResult.builder()
        .actual(toolName)
        .expected(expectedValue)
        .parameterName(parameterName)
        .isPassed(false)
        .userQuestion(userQuestion)
        .assertionType(assertionType.toString())
        .matchStrategy(matchStrategy.toString())
        .comment(e.getMessage())
        .accuracyScore(0.0)
        .createdTime(new Date())
        .build();
  }

  public record LlmEvalRequest(String llmResponse, String userQuestion, String expectedValue) {}
}
