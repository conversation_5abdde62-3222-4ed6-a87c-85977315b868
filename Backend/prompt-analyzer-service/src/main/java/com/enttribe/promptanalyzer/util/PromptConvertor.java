/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.dto.prompt.MessageConvertorDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptConvertorDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptDto;
import com.enttribe.promptanalyzer.model.Message;
import com.enttribe.promptanalyzer.model.Prompt;
import java.util.List;

/**
 * Utility class for converting Prompt entities to PromptConvertorDto objects. Provides methods to
 * convert individual entities and lists of entities.
 *
 * <AUTHOR>
 * @version 1.0
 */
public class PromptConvertor {

  /**
   * Private constructor to prevent instantiation of this utility class.
   */
  private PromptConvertor() {}

  /**
   * Converts a list of Prompt entities to a list of PromptConvertorDto objects.
   *
   * @param prompts the list of Prompt entities to convert
   * @return a list of converted PromptConvertorDto objects
   */
  public static List<PromptConvertorDto> getPromptDtoList(List<Prompt> prompts) {
    return prompts.stream().map(PromptConvertor::getPromptDto).toList();
  }

  /**
   * Converts a single Prompt entity to a PromptConvertorDto object.
   *
   * @param prompt the Prompt entity to convert
   * @return the converted PromptConvertorDto object
   */
  public static PromptConvertorDto getPromptDto(Prompt prompt) {
    String formattedMessages = PromptFooUtils.formatMessages(prompt.getMessages());

    return PromptConvertorDto.builder()
        .id(prompt.getNanoId())
        .application(prompt.getApplication())
        .category(prompt.getCategory())
        .promptId(prompt.getPromptId())
        .status(prompt.getStatus())
        .temperature(prompt.getTemperature())
        .maxTokens(prompt.getMaxToken())
        .version(prompt.getVersion())
        .prompt(formattedMessages)
        .topP(prompt.getTopP())
        .name(prompt.getName())
        .model(prompt.getLlmModel().getModel())
        .assertionTemplate(prompt.getAssertionTemplate())
        .defaultFormat(prompt.getDefaultFormat())
        .messages(getMessageDtos(prompt.getMessages()))
        .type(prompt.getType())
        .jsonMode(prompt.getJsonMode())
        .llmGuard(prompt.getLlmGuard())
        .provider(prompt.getLlmModel().getProvider())
        .tags(prompt.getTag())
        .promptName(prompt.getName())
        .applicationName(prompt.getApplication())
        .reasoningEffort(prompt.getReasoningEffort())
        .build();
  }

  /**
   * Converts a list of Message entities to a list of MessageConvertorDto objects.
   *
   * @param messages the list of Message entities to convert
   * @return a list of converted MessageConvertorDto objects, or an empty list if the input is null
   *     or empty
   */
  private static List<MessageConvertorDto> getMessageDtos(List<Message> messages) {
    if (messages == null || messages.isEmpty()) {
      return List.of();
    }

    return messages.stream()
        .map(
            message ->
                MessageConvertorDto.builder()
                    .role(message.getRole())
                    .content(message.getContent())
                    .build())
        .toList();
  }

  /**
   * Converts a Prompt entity to a PromptDto object.
   *
   * <p>This method maps all relevant fields from the Prompt entity to the
   * corresponding PromptDto, including nested objects like LLM model information
   * and messages. It handles null input gracefully by returning null.</p>
   *
   * @param prompt the Prompt entity to convert
   * @return the corresponding PromptDto, or null if input is null
   */
  public static PromptDto mapToPromptDto(Prompt prompt) {
    if (prompt == null) {
      return null;
    }
    return PromptDto.builder()
        .id(prompt.getNanoId())
        .application(prompt.getApplication())
        .category(prompt.getCategory())
        .promptId(prompt.getPromptId())
        .status(prompt.getStatus())
        .temperature(prompt.getTemperature())
        .maxTokens(prompt.getMaxToken())
        .version(prompt.getVersion())
        .topP(prompt.getTopP())
        .name(prompt.getName())
        .type(prompt.getType())
        .assertionTemplate(prompt.getAssertionTemplate())
        .defaultFormat(prompt.getDefaultFormat())
        .model(prompt.getLlmModel().getModel())
        .inference(prompt.getLlmModel().getInference())
        .provider(prompt.getLlmModel().getProvider())
        .jsonMode(prompt.getJsonMode())
        .llmGuard(prompt.getLlmGuard())
        .tags(prompt.getTag())
        .messages(prompt.getMessages())
        .reasoningEffort(prompt.getReasoningEffort())
        .build();
  }
}
