package com.enttribe.promptanalyzer.config;

import com.enttribe.core.generic.utils.XssSafeStringSerializer;
import com.enttribe.product.pii.filter.PropertyFilter;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import com.fasterxml.jackson.module.blackbird.BlackbirdModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;


@Configuration
public class JacksonConfig {

    @Bean
    public ObjectMapper objectMapper(Jackson2ObjectMapperBuilder builder) {
        ObjectMapper objectMapper = builder.build();

        // Register the XSS protection module
        objectMapper.registerModule(xssProtectionModule()); // Register if HTML content is going to database
        objectMapper.registerModule(new BlackbirdModule());


        // Create a SimpleFilterProvider
        SimpleFilterProvider filterProvider = new SimpleFilterProvider();

        // Add your custom filter to the filter provider if you need ABAC (Encrypted column or show hide)
        filterProvider.addFilter("propertyFilter", new PropertyFilter());
        objectMapper.setFilterProvider(filterProvider);
        return objectMapper;
    }

    public com.fasterxml.jackson.databind.Module xssProtectionModule() {
        SimpleModule module = new SimpleModule();
        module.addSerializer(String.class, new XssSafeStringSerializer());
        return module;
    }


}
