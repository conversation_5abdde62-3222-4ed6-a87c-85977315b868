/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.dto.testsuite.TestSuiteRequestDto;
import com.enttribe.promptanalyzer.dto.testsuite.TestSuiteResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.TestSuite;
import java.util.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public final class TestSuiteUtils {

  /**
   * Private constructor to prevent instantiation of utility class. This class should not be
   * instantiated, as it only contains static methods.
   */
  private TestSuiteUtils() {}

  /**
   * Converts a TestSuiteRequestDto to a TestSuite entity. Only sets fields if the corresponding
   * value in the DTO is not null.
   *
   * @param dto the TestSuiteRequestDto to convert
   * @param testSuite the TestSuite entity to update
   */
  public static void mapToEntity(TestSuiteRequestDto dto, TestSuite testSuite) {
    try {
      if (dto.getName() != null) {
        testSuite.setName(dto.getName());
      }
      if (dto.getAgentId() != null) {
        testSuite.setAgentId(dto.getAgentId());
      }
      if (dto.getAgentType() != null) {
        testSuite.setAgentType(dto.getAgentType());
      }
      if (dto.getAgentName() != null) {
        testSuite.setAgentName(dto.getAgentName());
      }
    } catch (Exception e) {
      log.error("Error mapping TestSuiteRequestDto to TestSuite entity: {}", e.getMessage(), e);
      throw new BusinessException("Failed to map TestSuiteRequestDto to entity");
    }
  }

  /**
   * Converts a TestSuite entity to a TestSuiteResponseDto object.
   *
   * @param testSuite the TestSuite entity to convert
   * @return the converted TestSuiteResponseDto object
   */
  public static TestSuiteResponseDto getTestSuiteResponseDto(TestSuite testSuite) {
    try {
      return TestSuiteResponseDto.builder()
          .id(testSuite.getNanoId())
          .name(testSuite.getName())
          .testCaseCount(getAgentTestCaseCount(testSuite))
          .deleted(testSuite.getDeleted())
          .modifiedTime(testSuite.getModifiedTime())
          .createdTime(testSuite.getCreatedTime())
          .agentId(testSuite.getAgentId())
          .agentName(testSuite.getAgentName())
          .agentType(testSuite.getAgentType())
          .build();
    } catch (Exception e) {
      log.error("Error converting TestSuite to TestSuiteResponseDto: {}", e.getMessage(), e);
      throw new BusinessException("Failed to convert TestSuite to response DTO");
    }
  }

  /**
   * Counts the number of non-deleted agent test cases in a test suite.
   *
   * @param testSuite the test suite to count agent test cases for
   * @return the count of non-deleted agent test cases
   */
  private static int getAgentTestCaseCount(TestSuite testSuite) {
    return (int)
        testSuite.getAgentTestCases().stream()
            .filter(agentTestCase -> !agentTestCase.isDeleted())
            .count();
  }

  /**
   * Converts a list of TestSuite entities to a list of TestSuiteResponseDto objects.
   *
   * @param testSuites the list of TestSuite entities to convert
   * @return the converted list of TestSuiteResponseDto objects, or empty list if input is null
   */
  public static List<TestSuiteResponseDto> getTestSuiteDtoList(List<TestSuite> testSuites) {
    if (testSuites == null) {
      return Collections.emptyList();
    }
    return testSuites.stream().map(TestSuiteUtils::getTestSuiteResponseDto).toList();
  }
}
