/*
 * Copyright (c) 2024 Vwaves Technologies Private Limited
 *
 * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.
 * Product: Aspose.Total for Java
 * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations
 * Order ID: 250610115636
 *
 * This source code is part of a proprietary software system and may not be
 * distributed, modified, or reused without explicit written permission from
 * Vwaves Technologies Private Limited.
 *
 * Contact: <EMAIL>
 */
package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.AgentTestCase;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * Data Access Object interface for AgentTestCase entity operations. Provides methods to interact
 * with the AgentTestCase table in the database for managing agent test cases.
 *
 * <p>This interface extends JpaRepository to provide standard CRUD operations and includes
 * custom query methods for retrieving agent test cases by nanoId, name, and multiple nanoIds.</p>
 *
 * @see AgentTestCase
 * @see JpaRepository
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public interface AgentTestCaseDao extends JpaRepository<AgentTestCase, Long> {

  /**
   * Finds an agent test case by its nanoId.
   *
   * @param nanoId the nanoId to search for
   * @return an Optional containing the agent test case if found, empty otherwise
   */
  Optional<AgentTestCase> findByNanoId(String nanoId);

  /**
   * Finds an agent test case by its name.
   *
   * @param name the name to search for
   * @return an Optional containing the agent test case if found, empty otherwise
   */
  Optional<AgentTestCase> findByName(String name);

  /**
   * Finds all agent test cases by their nanoIds.
   *
   * @param nanoIds the list of nanoIds to search for
   * @return a list of agent test cases matching the provided nanoIds
   */
  @Query("SELECT a FROM AgentTestCase a WHERE a.nanoId IN :nanoIds")
  List<AgentTestCase> findByNanoIdIn(List<String> nanoIds);
}
