/*
 * Copyright (c) 2024 Vwaves Technologies Private Limited
 *
 * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.
 * Product: Aspose.Total for Java
 * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations
 * Order ID: 250610115636
 *
 * This source code is part of a proprietary software system and may not be
 * distributed, modified, or reused without explicit written permission from
 * Vwaves Technologies Private Limited.
 *
 * Contact: <EMAIL>
 */
package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.TestSuite;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Data Access Object interface for TestSuite entity operations. Provides methods to interact
 * with the TestSuite table in the database for managing test suites and their associated
 * agent test cases.
 *
 * <p>This interface extends JpaRepository to provide standard CRUD operations and includes
 * custom query methods for retrieving test suites with their associated data.</p>
 *
 * @see TestSuite
 * @see JpaRepository
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public interface TestSuiteDao extends JpaRepository<TestSuite, Integer> {

  /**
   * Finds a test suite by its nanoId that is not deleted and eagerly loads its agent test cases.
   *
   * @param nanoId the nanoId to search for
   * @return an Optional containing the test suite with loaded agent test cases if found and not deleted, empty otherwise
   */
  @Query(
      "SELECT t FROM TestSuite t LEFT JOIN FETCH t.agentTestCases WHERE t.nanoId = :nanoId and t.deleted = false")
  Optional<TestSuite> findByNanoId(String nanoId);

  /**
   * Finds a test suite by its nanoId that is not deleted and eagerly loads all associated data
   * including agent test cases, tools, assertions, knowledge bases, and prompts.
   *
   * @param nanoId the nanoId to search for
   * @return an Optional containing the test suite with all associated data if found and not deleted, empty otherwise
   */
  @Query(
      """
            SELECT DISTINCT t FROM TestSuite t
            LEFT JOIN FETCH t.agentTestCases atc
            LEFT JOIN FETCH atc.tools
            LEFT JOIN FETCH atc.agentTestCaseAssertions
            LEFT JOIN FETCH atc.knowledgeBases
            LEFT JOIN FETCH atc.prompt
            WHERE t.nanoId = :nanoId AND t.deleted = false
            """)
  Optional<TestSuite> findByNanoIdWithAll(@Param("nanoId") String nanoId);
}
