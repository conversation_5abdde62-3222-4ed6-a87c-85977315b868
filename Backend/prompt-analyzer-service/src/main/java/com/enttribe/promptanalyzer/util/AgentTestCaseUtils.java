/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.enttribe.promptanalyzer.dto.agent.AgentTestCaseAssertionReponseDto;
import com.enttribe.promptanalyzer.dto.agent.AgentTestCaseRequestDto;
import com.enttribe.promptanalyzer.dto.agent.AgentTestCaseResponseDto;
import com.enttribe.promptanalyzer.model.*;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

/**
 * Utility class for converting between AgentTestCase entities and DTOs.
 *
 * <p>This class provides static methods for converting AgentTestCase entities to and from
 * their corresponding DTOs. It handles the mapping of all relevant fields including
 * nested objects like assertions, tools, and knowledge bases. The utility methods
 * ensure proper data transformation while maintaining data integrity.</p>
 *
 * <p>The class supports both entity-to-DTO and DTO-to-entity conversions, with proper
 * handling of null values and collections. It also generates unique IDs using NanoId
 * for new entities.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 * @see AgentTestCase
 * @see AgentTestCaseRequestDto
 * @see AgentTestCaseResponseDto
 * @see AgentTestCaseAssertionReponseDto
 */
@Slf4j
public class AgentTestCaseUtils {

  /**
   * Private constructor to prevent instantiation of this utility class.
   */
  private AgentTestCaseUtils() {}

  /**
   * Converts an AgentTestCaseRequestDto to an AgentTestCase entity.
   *
   * <p>This method creates a new AgentTestCase entity from the provided DTO and
   * associated objects. It sets all relevant fields including generated NanoId,
   * timestamps, and relationships with Prompt, Tools, and KnowledgeBases.
   * The method handles null collections by creating empty sets.</p>
   *
   * @param dto the request DTO containing agent test case data
   * @param prompt the associated prompt entity
   * @param tools the set of tools associated with the test case
   * @param knowledgeBases the set of knowledge bases associated with the test case
   * @return the created AgentTestCase entity
   */
  public static AgentTestCase toEntity(
      AgentTestCaseRequestDto dto,
      Prompt prompt,
      Set<Tool> tools,
      Set<KnowledgeBase> knowledgeBases) {
    AgentTestCase entity = new AgentTestCase();
    entity.setNanoId(NanoIdUtils.randomNanoId());
    entity.setName(dto.getName());
    entity.setAgentName(dto.getAgentName());
    entity.setAgentId(dto.getAgentId());
    entity.setAgentType(dto.getAgentType());
    entity.setDescription(dto.getDescription());
    entity.setHistory(dto.getHistory());
    entity.setAdvisors(dto.getAdvisors());
    entity.setDeleted(Boolean.TRUE.equals(dto.getDeleted()));
    entity.setPrompt(prompt);
    entity.setTools(tools != null ? tools : new HashSet<>());
    entity.setKnowledgeBases(knowledgeBases != null ? knowledgeBases : new HashSet<>());
    entity.setUserQuestion(dto.getUserQuestion());
    entity.setCreatedTime(new Date());
    entity.setModifiedTime(new Date());
    entity.setVariableMap(dto.getVariableMap());
    return entity;
  }

  /**
   * Converts an AgentTestCase entity to an AgentTestCaseResponseDto.
   *
   * <p>This method maps all relevant fields from the AgentTestCase entity to the
   * corresponding response DTO. It includes nested assertions and handles null
   * collections gracefully by converting them to empty lists. The method provides
   * a complete view of the test case including all associated data.</p>
   *
   * @param agentTestCase the AgentTestCase entity to convert
   * @return the corresponding AgentTestCaseResponseDto
   */
  public static AgentTestCaseResponseDto toResponseDto(AgentTestCase agentTestCase) {
    return AgentTestCaseResponseDto.builder()
        .id(agentTestCase.getNanoId())
        .agentId(agentTestCase.getAgentId())
        .agentName(agentTestCase.getAgentName())
        .agentType(agentTestCase.getAgentType())
        .name(agentTestCase.getName())
        .description(agentTestCase.getDescription())
        .history(agentTestCase.getHistory())
        .advisors(agentTestCase.getAdvisors())
        .deleted(agentTestCase.isDeleted())
        .createdTime(agentTestCase.getCreatedTime())
        .modifiedTime(agentTestCase.getModifiedTime())
        .userQuestion(agentTestCase.getUserQuestion())
        .variableMap(agentTestCase.getVariableMap())
        .assertions(
            Optional.ofNullable(agentTestCase.getAgentTestCaseAssertions())
                .orElse(Collections.emptySet())
                .stream()
                .map(AgentTestCaseUtils::toAssertionResponseDto) // calling your new method
                .toList())
        .build();
  }

  /**
   * Converts an AgentTestCaseAssertion entity to an AgentTestCaseAssertionReponseDto.
   *
   * <p>This private method maps assertion data from the entity to the response DTO.
   * It extracts the assertion type, expected value, JSON pointer, tool name, and
   * match strategy from the nested assertion object.</p>
   *
   * @param assertion the AgentTestCaseAssertion entity to convert
   * @return the corresponding AgentTestCaseAssertionReponseDto
   */
  private static AgentTestCaseAssertionReponseDto toAssertionResponseDto(
      AgentTestCaseAssertion assertion) {
    String expectedValue = assertion.getExpectedValue();
    String jsonPointer = assertion.getParameterName();

    AgentTestCaseAssertionReponseDto dto = new AgentTestCaseAssertionReponseDto();
    dto.setId(assertion.getAssertion().getNanoId());
    dto.setAssertionType(
        assertion.getAssertion() != null
            ? String.valueOf(assertion.getAssertion().getAssertionType())
            : null);
    dto.setExpectedValue(expectedValue);
    dto.setJsonPointer(jsonPointer);
    dto.setToolName(assertion.getToolName());
    dto.setMatchStrategy(assertion.getAssertion().getMatchStrategy().toString());
    return dto;
  }
}
