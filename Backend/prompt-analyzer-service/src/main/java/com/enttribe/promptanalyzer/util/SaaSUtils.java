/*
 * Copyright (c) 2024 Vwaves Technologies Private Limited
 *
 * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.
 * Product: Aspose.Total for Java
 * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations
 * Order ID: 250610115636
 *
 * This source code is part of a proprietary software system and may not be
 * distributed, modified, or reused without explicit written permission from
 * Vwaves Technologies Private Limited.
 *
 * Contact: <EMAIL>
 */
package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.aspect.TenantFilterAspect;
import com.enttribe.promptanalyzer.constants.FilterConstants;
import com.enttribe.promptanalyzer.enums.CustomerType;
import com.enttribe.promptanalyzer.model.BaseEntityGlobal;
import jakarta.persistence.EntityManager;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.Session;
import org.jetbrains.annotations.NotNull;

/**
 * Utility class for SaaS (Software as a Service) operations including deduplication,
 * tenant filtering, and entity management.
 *
 * <p>This class provides methods for handling multi-tenant data operations with
 * deduplication capabilities and Hibernate filter management.</p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class SaaSUtils {

  private SaaSUtils() {
    // Private constructor to prevent instantiation
  }

  /**
   * Deduplicates a list of items based on a key extractor function, prioritizing
   * non-tenant-1 items over tenant-1 items.
   *
   * @param <T> the type of items in the list
   * @param items the list of items to deduplicate
   * @param keyExtractor function to extract the key for deduplication
   * @return a deduplicated list of items
   */
  @SuppressWarnings("java:S3824") // computeIfAbsent not suitable due to tenantId condition
  public static <T> List<T> deduplicateAndGetList(List<T> items, Function<T, String> keyExtractor) {
    if (items == null || items.isEmpty()) {
      return new ArrayList<>();
    }
    Map<String, T> map = new LinkedHashMap<>();
    for (T item : items) {
      Integer tenantId = ((BaseEntityGlobal) item).getCustomerId();
      String key = keyExtractor.apply(item);

      if (!map.containsKey(key) || tenantId != 1) {
        map.put(key, item);
      }
    }
    return new ArrayList<>(map.values());
  }

  /**
   * Deduplicates a list of items and returns a single item, prioritizing
   * non-tenant-1 items over tenant-1 items.
   *
   * @param <T> the type of items in the list
   * @param items the list of items to deduplicate
   * @param keyExtractor function to extract the key for deduplication
   * @return a single deduplicated item, or null if the list is empty
   * @throws IllegalStateException if multiple records are found for the same key
   */
  @SuppressWarnings("java:S3824") // computeIfAbsent not suitable due to tenantId condition
  public static <T> T deduplicateAndGetOne(List<T> items, Function<T, String> keyExtractor) {
    if (items == null || items.isEmpty()) {
      return null;
    }
    Map<String, T> map = new LinkedHashMap<>();
    for (T item : items) {
      Integer tenantId = ((BaseEntityGlobal) item).getCustomerId();
      String key = keyExtractor.apply(item);

      if (!map.containsKey(key) || tenantId != 1) {
        map.put(key, item);
      }
    }
    ArrayList<T> records = new ArrayList<>(map.values());
    if (records.size() > 1) {
      throw new IllegalStateException(
          "Multiple records found for key: " + keyExtractor.apply(records.getFirst()));
    }
    return records.isEmpty() ? null : records.getFirst();
  }

  /**
   * Deduplicates a list of items and returns an optional single item, prioritizing
   * non-tenant-1 items over tenant-1 items.
   *
   * @param <T> the type of items in the list
   * @param items the list of items to deduplicate
   * @param keyExtractor function to extract the key for deduplication
   * @return an Optional containing a single deduplicated item, or empty if the list is empty
   * @throws IllegalStateException if multiple records are found for the same key
   */

  @SuppressWarnings("java:S3824") // computeIfAbsent not suitable due to tenantId condition
  public static <T> Optional<T> deduplicateAndGetOptional(
      List<T> items, Function<T, String> keyExtractor) {
    if (items == null || items.isEmpty()) {
      return Optional.empty();
    }
    Map<String, T> map = new LinkedHashMap<>();
    for (T item : items) {
      Integer tenantId = ((BaseEntityGlobal) item).getCustomerId();
      String key = keyExtractor.apply(item);

      if (!map.containsKey(key) || tenantId != 1) {
        map.put(key, item);
      }
    }
    ArrayList<T> records = new ArrayList<>(map.values());
    if (records.size() > 1) {
      throw new IllegalStateException(
          "Multiple records found for key: " + keyExtractor.apply(records.getFirst()));
    }
    return records.isEmpty() ? Optional.empty() : Optional.of(records.getFirst());
  }

  /**
   * Disables the default customer ID filter on the given EntityManager.
   *
   * <p>This method disables the default customer ID filter and updates the filter state
   * to prevent further filter operations. It's used when you need to perform operations
   * without tenant filtering.</p>
   *
   * @param entityManager the EntityManager to disable the filter on
   */
  public static void disableFilter(EntityManager entityManager) {
    log.info("disabling filter - {}", FilterConstants.DEFAULT_CUSTOMER_ID_FILTER);
      TenantFilterAspect.filterState.set(Boolean.TRUE);
    entityManager.unwrap(Session.class).disableFilter(FilterConstants.DEFAULT_CUSTOMER_ID_FILTER);
  }

  /**
   * Disables a specific filter on the given EntityManager.
   *
   * <p>This method disables the specified filter and updates the filter state
   * to prevent further filter operations. It's used when you need to perform operations
   * without specific tenant filtering.</p>
   *
   * @param entityManager the EntityManager to disable the filter on
   * @param filterName the name of the filter to disable
   */
  public static void disableFilter(EntityManager entityManager, String filterName) {
    log.info("disabling filter : {}", filterName);
      TenantFilterAspect.filterState.set(Boolean.TRUE);
    entityManager.unwrap(Session.class).disableFilter(filterName);
  }

  /**
   * Enables a specific filter on the given EntityManager with customer ID parameter.
   *
   * <p>This method enables the specified filter with the given customer ID parameter.
   * It's used to apply tenant-specific filtering to database operations.</p>
   *
   * @param entityManager the EntityManager to enable the filter on
   * @param filterName the name of the filter to enable
   * @param customerId the customer ID to use as the filter parameter
   */
  public static void enableFilter(
      EntityManager entityManager, String filterName, Integer customerId) {
    log.info("enabling filter : {}", filterName);
    entityManager
        .unwrap(Session.class)
        .enableFilter(filterName)
        .setParameter("customerId", customerId);
  }

  /**
   * Filters a list of items to return only global (tenant-1) items.
   *
   * <p>This method filters the input list to return only items that belong to
   * tenant-1 (global tenant). It uses the key extractor to deduplicate items
   * based on the extracted key.</p>
   *
   * @param <T> the type of items in the list
   * @param items the list of items to filter
   * @param keyExtractor function to extract the key for deduplication
   * @return a list containing only global items
   */
  public static <T> List<T> getGlobal(List<T> items, Function<T, String> keyExtractor) {
    if (items == null || items.isEmpty()) {
      return new ArrayList<>();
    }
    Map<String, T> map = new LinkedHashMap<>();
    for (T item : items) {
      Integer tenantId = ((BaseEntityGlobal) item).getCustomerId();
      String key = keyExtractor.apply(item);

      if (tenantId == 1) {
        map.put(key, item);
      }
    }
    return new ArrayList<>(map.values());
  }

  /**
   * Filters a list of items to return only local (non-tenant-1) items.
   *
   * <p>This method filters the input list to return only items that belong to
   * non-tenant-1 (local tenants). It uses the key extractor to deduplicate items
   * based on the extracted key.</p>
   *
   * @param <T> the type of items in the list
   * @param items the list of items to filter
   * @param keyExtractor function to extract the key for deduplication
   * @return a list containing only local items
   */
  public static <T> List<T> getLocal(List<T> items, Function<T, String> keyExtractor) {
    if (items == null || items.isEmpty()) {
      return new ArrayList<>();
    }
    Map<String, T> map = new LinkedHashMap<>();
    for (T item : items) {
      Integer tenantId = ((BaseEntityGlobal) item).getCustomerId();
      String key = keyExtractor.apply(item);

      if (tenantId != 1) {
        map.put(key, item);
      }
    }
    return new ArrayList<>(map.values());
  }

    /**
     * Determines the customer type based on the isLocal flag.
     *
     * <p>This method converts a boolean flag indicating whether a customer is local
     * into the corresponding CustomerType enum value.</p>
     *
     * @param isLocal boolean flag indicating if the customer is local
     * @return CustomerType.LOCAL if isLocal is true, CustomerType.GLOBAL otherwise
     */
    @NotNull
    public static CustomerType getCustomerType(Boolean isLocal) {
        CustomerType customerType;
        if (isLocal.equals(true))
            customerType = CustomerType.LOCAL;
        else
            customerType = CustomerType.GLOBAL;
        return customerType;
    }

}
