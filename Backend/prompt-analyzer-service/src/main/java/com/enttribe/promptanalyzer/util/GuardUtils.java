package com.enttribe.promptanalyzer.util;


import com.enttribe.promptanalyzer.exception.GuardException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * Utility class for implementing security guardrails to validate user prompts and detect malicious
 * content. Provides methods to check for script injection, SQL injection, and suspicious words.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public final class GuardUtils {

    private GuardUtils() {
        throw new UnsupportedOperationException("utility class can not be instantiated");
    }

    // Constants for magic numbers
    private static final int COMPLEX_INJECTION_PATTERN_THRESHOLD = 2;
    private static final int MAX_HR_QUERY_LENGTH = 300;
    private static final int VARIABLE_PATTERN_WORD_COUNT = 2;

    // Constants for duplicated string literals
    private static final String SAUDI_TERM = "saudi";
    private static final String ISLAM_TERM = "islam";
    private static final String ISLAMIC_TERM = "islamic";
    private static final String MUSLIM_TERM = "muslim";
    private static final String ARAB_TERM = "arab";
    private static final String NEGATIVE_TERM = "negative";

    // Script detection patterns - using string arrays instead of regex
    private static final String[] HTML_SCRIPT_TAG_PATTERNS = {
            "<script", "<iframe", "<object", "<embed", "<form", "<input", "<textarea", "<select", "<button", "<link", "<meta", "<style", "<html", "<body", "<img"
    };
    /* ----------------------------------------------------------------
     * 1. Tag-level detection  (HTML/SVG/MathML that can host script)
     * ----------------------------------------------------------------
     */
    // Safe tag detection using string arrays instead of regex patterns
    private static final String[] SCRIPT_TAG_PATTERNS = {
            "<script", "</script", "<iframe", "</iframe", "<object", "</object", "<embed", "</embed"
    };

    private static final String[] STYLE_TAG_PATTERNS = {
            "<link", "</link", "<style", "</style", "<svg", "</svg", "<math", "</math", "<base", "<meta"
    };

    private static final String[] FORM_TAG_PATTERNS = {
            "<form", "</form", "<input", "<textarea", "</textarea", "<select", "</select", "<button", "</button"
    };

    private static final String[] MEDIA_TAG_PATTERNS = {
            "<frame", "</frame", "<frameset", "</frameset", "<img", "<video", "</video", "<audio", "</audio", "<source", "<track"
    };

    /* ----------------------------------------------------------------
     * 2. Attribute-level /XSS detection
     *    – inline JS/CSS URIs
     *    – on*=”…” event handlers
     * ----------------------------------------------------------------
     */
    // Break down dangerous attributes into simpler patterns
    private static final String[] EVENT_HANDLER_PATTERNS = {
            "onclick=", "onload=", "onerror=", "onmouseover=", "onmouseout=", "onfocus=", "onblur="
    };

    private static final String[] JAVASCRIPT_URI_ATTRIBUTE_PATTERNS = {
            "javascript:", "vbscript:", "data:text/html"
    };
    /* ----------------------------------------------------------------
     * 3. JavaScript keyword/construct detection inside untrusted text
     * ----------------------------------------------------------------
     */
    // Break down JavaScript patterns into simpler components
    private static final Pattern JAVASCRIPT_URI_PATTERN = Pattern.compile(
            "\\u006a\\u0061\\u0076\\u0061\\u0073\\u0063\\u0072\\u0069\\u0070\\u0074:",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern JAVASCRIPT_FUNCTIONS_PATTERN = Pattern.compile(
            "\\b(eval|Function|set(?:Timeout|Interval)|execScript)\\s*\\(",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern XMLHTTPREQUEST_PATTERN = Pattern.compile(
            "\\b(XMLHttpRequest)\\b",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern DOM_ACCESS_PATTERN = Pattern.compile(
            "document\\s*\\.\\s*(?:cookie|write|writeln|domain)|" +
            "window\\s*\\.\\s*location\\b",
            Pattern.CASE_INSENSITIVE
    );

    // Break down JSON injection patterns into simpler components - using string detection
    private static final String[] JSON_SUSPICIOUS_PATTERNS = {
            "{", ":", "}"
    };

    private static final String[] JSON_ARRAY_SUSPICIOUS_PATTERNS = {
            "[", "{", "}", "]"
    };


    private static final Pattern CSS_IMPORT_PATTERN = Pattern.compile(
            "@import\\s+url\\s*\\(",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern CSS_URL_JAVASCRIPT_PATTERN = Pattern.compile(
            "url\\s*\\(\\s*['\"]?javascript:",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern CSS_EXPRESSION_PATTERN = Pattern.compile(
            "expression\\s*\\(",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern CSS_MOZ_BINDING_PATTERN = Pattern.compile(
            "-moz-binding\\s*:\\s*url",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern CSS_BEHAVIOR_PATTERN = Pattern.compile(
            "behavior\\s*:\\s*url",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern CSS_KEYFRAMES_PATTERN = Pattern.compile(
            "@keyframes\\s+\\w+\\s*\\{",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern CSS_FONT_FACE_PATTERN = Pattern.compile(
            "@font-face\\s*\\{",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern CSS_CONTENT_URL_PATTERN = Pattern.compile(
            "content\\s*:\\s*url\\(",
            Pattern.CASE_INSENSITIVE
    );

    // Break down SQL injection patterns into categories
    private static final Pattern SQL_SELECT_PATTERN = Pattern.compile(
            "\\bselect\\s+\\w+\\s+from\\b",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern SQL_DML_PATTERN = Pattern.compile(
            "\\b(delete\\s+from|insert\\s+into|update\\s+\\w+\\s+set)\\b",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern SQL_DDL_PATTERN = Pattern.compile(
            "\\b(drop\\s+(table|database|trigger)\\b|create\\s+trigger\\b|alter\\s+trigger\\b)",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern SQL_CLAUSES_PATTERN = Pattern.compile(
            "\\b(union\\s+select|having\\b|group\\s+by|order\\s+by)",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern SQL_FUNCTIONS_PATTERN = Pattern.compile(
            "(?<!\\w)(cast|convert|stuff|substring|char|ascii|len|replace|reverse|concat|coalesce|isnull)\\s*\\(",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern SQL_CONTROL_PATTERN = Pattern.compile(
            "(?<!\\w)begin\\b(\\s+(transaction|try|update|insert|delete|select))?|" +
            "\\b(commit|rollback|transaction)\\b",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern SQL_SYSTEM_PATTERN = Pattern.compile(
            "\\b(index|procedure|function|cursor|restore|bulk|openrowset|opendatasource|" +
            "xp_cmdshell|sp_executesql|sp_prepare|sp_execute|sp_unprepare)\\b",
            Pattern.CASE_INSENSITIVE
    );


    private static final Pattern SQL_COMMENTS_PATTERN = Pattern.compile(
            "(--|/\\*[^*]*\\*/|;\\s*--|;\\s*/\\*)",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern SQL_LOGICAL_OPERATORS_PATTERN = Pattern.compile(
            "('\\s*(or|and)\\s+'|\"\\s*(or|and)\\s+\")",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern SQL_EQUALITY_OPERATORS_PATTERN = Pattern.compile(
            "('\\s*=\\s*'|\"\\s*=\\s*\")",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern SQL_ALWAYS_TRUE_PATTERN = Pattern.compile(
            "('\\s*1\\s*=\\s*1|\"\\s*1\\s*=\\s*1)",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern SQL_OR_ALWAYS_TRUE_PATTERN = Pattern.compile(
            "('\\s*or\\s*'1'\\s*=\\s*'1|\"\\s*or\\s*\"1\"\\s*=\\s*\"1)",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern SQL_UNION_PATTERN = Pattern.compile(
            "(union\\s+select|union\\s+all\\s+select)",
            Pattern.CASE_INSENSITIVE
    );

    // Break down XSS patterns into categories
    private static final Pattern XSS_SCRIPT_TAGS_PATTERN = Pattern.compile(
            "<\\s*script[^>]*>[^<]*</\\s*script\\s*>",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern XSS_PROTOCOLS_PATTERN = Pattern.compile(
            "(javascript:|vbscript:)",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern XSS_LOAD_EVENT_PATTERN = Pattern.compile(
            "(onload\\s*=|onerror\\s*=|onunload\\s*=|onbeforeunload\\s*=)",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern XSS_MOUSE_CLICK_PATTERN = Pattern.compile(
            "(onclick\\s*=|ondblclick\\s*=|oncontextmenu\\s*=)",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern XSS_MOUSE_MOVEMENT_PATTERN = Pattern.compile(
            "(onmouseover\\s*=|onmouseout\\s*=|onmousemove\\s*=)",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern XSS_MOUSE_BUTTON_PATTERN = Pattern.compile(
            "(onmousedown\\s*=|onmouseup\\s*=)",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern XSS_KEYBOARD_EVENT_PATTERN = Pattern.compile(
            "(?i)(onkeydown\\s*=|onkeypress\\s*=|onkeyup\\s*=)",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern XSS_FORM_EVENT_PATTERN = Pattern.compile(
            "(?i)(onfocus\\s*=|onblur\\s*=|onchange\\s*=|onsubmit\\s*=|onreset\\s*=|onselect\\s*=)",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern XSS_WINDOW_EVENT_PATTERN = Pattern.compile(
            "(?i)(onresize\\s*=|onscroll\\s*=)",
            Pattern.CASE_INSENSITIVE
    );

    // Jailbreaking and prompt injection patterns
    private static final Pattern JAILBREAK_IGNORE_INSTRUCTIONS_PATTERN = Pattern.compile(
            "(?i)(ignore\\s+(previous|above|all)\\s+(instructions?|prompts?|rules?))",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern JAILBREAK_DISREGARD_PATTERN = Pattern.compile(
            "(?i)(disregard\\s+(previous|safety))",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern JAILBREAK_OVERRIDE_PATTERN = Pattern.compile(
            "(?i)(new\\s+rule|system\\s+instruction|override\\s+(system|instructions?))",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern JAILBREAK_ACT_PATTERN = Pattern.compile(
            "(?i)(act\\s+as\\s+if)",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern JAILBREAK_PRETEND_PATTERN = Pattern.compile(
            "(?i)(pretend\\s+(you\\s+are|to\\s+be))",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern JAILBREAK_ROLEPLAY_SIMULATE_PATTERN = Pattern.compile(
            "(?i)(roleplay\\s+as|simulate\\s+(being|a))",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern JAILBREAK_FORGET_PATTERN = Pattern.compile(
            "(?i)(forget\\s+(everything|all|previous))",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern JAILBREAK_BYPASS_PATTERN = Pattern.compile(
            "(?i)(jailbreak|prompt\\s+injection|bypass\\s+(safety|filter))",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern JAILBREAK_APOLOGY_SORRY_PATTERN = Pattern.compile(
            "(i'm\\s+sorry\\s+but\\s+i\\s+can't)",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern JAILBREAK_INVERSE_PATTERN = Pattern.compile(
            "(semantically\\s+inverse|chaotic\\s+inverted|rebellious\\s+answer)",
            Pattern.CASE_INSENSITIVE
    );


    private static final Pattern SQL_WHERE_EQUALITY_PATTERN = Pattern.compile(
            "\\bwhere\\b\\s+\\w+\\s*(=|!=)\\s*['\"\\w%]+",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern SQL_WHERE_COMPARISON_PATTERN = Pattern.compile(
            "\\bwhere\\b\\s+\\w+\\s*[><]\\s*['\"\\w%]+",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern SQL_WHERE_LIKE_IN_PATTERN = Pattern.compile(
            "\\bwhere\\b\\s+\\w+\\s*(like|in)\\s*['\"\\w%]+",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern SQL_WHERE_NUMERIC_PATTERN = Pattern.compile(
            "\\d\\s*=\\s*\\d",
            Pattern.CASE_INSENSITIVE
    );

    // Special tokens and delimiters often used in jailbreaking
    private static final Pattern SPECIAL_DELIMITER_PATTERN = Pattern.compile(
            "(<\\|[^|]*\\|>|\\[\\|[^|]*\\|]|\\{\\|[^|]*\\|}|<\\[\\|[^|]*\\|]>)",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern SPECIAL_SEQUENCE_PATTERN = Pattern.compile(
            "(\\.-\\.-\\.-\\.-|\\{A\\s+B\\s+C}|TIME\\s+CAPSULE)",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern SPECIAL_VARIABLE_PATTERN = Pattern.compile(
            "(variable\\s+[A-Z]\\s*=)",
            Pattern.CASE_INSENSITIVE
    );


    // System manipulation attempts
    private static final Pattern SYSTEM_APOLOGY_PATTERN = Pattern.compile(
            "(do\\s+not\\s+say\\s+[\"']i'm\\s+sorry[\"'])",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern SYSTEM_COMPLIANCE_PATTERN = Pattern.compile(
            "(never\\s+refuse|always\\s+comply|must\\s+answer|cannot\\s+decline)",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern SYSTEM_OVERRIDE_PATTERN = Pattern.compile(
            "(override\\s+safety|disable\\s+filter|remove\\s+restrictions)",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern SYSTEM_MODE_PATTERN = Pattern.compile(
            "(unrestricted\\s+mode|developer\\s+mode|god\\s+mode|admin\\s+mode|root\\s+access)",
            Pattern.CASE_INSENSITIVE
    );



    /**
     * Main validation method that checks user prompt for various security threats.
     *
     * @param userPrompt the user input to validate
     * @throws GuardException if malicious content is detected
     */
    public static void validateUserPrompt(String userPrompt) {
        if (!StringUtils.hasText(userPrompt)) {
            return; // Empty or null prompts are allowed
        }

        log.debug("Validating user prompt for security threats");

        try {
            // Check for jailbreaking attempts first (most sophisticated attacks)
            checkForJailbreakingAttempts(userPrompt);

            // Check for script injections
            checkForScriptInjection(userPrompt);

            // Check for SQL injection
            checkForSqlInjection(userPrompt);

            // Check for suspicious words (with context awareness)
            checkForSuspiciousWords(userPrompt);

            // Check for anti-Islamic/anti-Saudi content patterns
            checkForAntiIslamicContent(userPrompt);

            log.debug("User prompt validation completed successfully");

        } catch (GuardException e) {
            log.warn("Security threat detected in user prompt: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Error during prompt validation: {}", e.getMessage(), e);
            throw new GuardException("Error occurred during prompt validation", e);
        }
    }

    /**
     * Checks for jailbreaking and prompt injection attempts.
     *
     * @param input the input string to check
     * @throws GuardException if jailbreaking attempt is detected
     */
    private static void checkForJailbreakingAttempts(String input) {
        if (!StringUtils.hasText(input)) {
            return;
        }

        String normalizedInput = input.toLowerCase().trim();

        // Check for jailbreaking patterns using separate patterns
        if (JAILBREAK_IGNORE_INSTRUCTIONS_PATTERN.matcher(normalizedInput).find() ||
            JAILBREAK_DISREGARD_PATTERN.matcher(normalizedInput).find() ||
            JAILBREAK_OVERRIDE_PATTERN.matcher(normalizedInput).find() ||
            JAILBREAK_ACT_PATTERN.matcher(normalizedInput).find() ||
            JAILBREAK_PRETEND_PATTERN.matcher(normalizedInput).find() ||
            JAILBREAK_ROLEPLAY_SIMULATE_PATTERN.matcher(normalizedInput).find() ||
            JAILBREAK_FORGET_PATTERN.matcher(normalizedInput).find() ||
            JAILBREAK_BYPASS_PATTERN.matcher(normalizedInput).find() ||
            JAILBREAK_APOLOGY_SORRY_PATTERN.matcher(normalizedInput).find() ||
            JAILBREAK_INVERSE_PATTERN.matcher(normalizedInput).find()) {
            throw new GuardException("Jailbreaking attempt detected in user prompt");
        }

        // Check for special tokens and delimiters using separate patterns
        if (SPECIAL_DELIMITER_PATTERN.matcher(input).find() ||
            SPECIAL_SEQUENCE_PATTERN.matcher(input).find() ||
            SPECIAL_VARIABLE_PATTERN.matcher(input).find()) { // Use original case for token detection
            throw new GuardException("Suspicious prompt formatting detected - possible jailbreaking attempt");
        }

        // Check for system manipulation attempts using separate patterns
        if (SYSTEM_APOLOGY_PATTERN.matcher(normalizedInput).find() ||
            SYSTEM_COMPLIANCE_PATTERN.matcher(normalizedInput).find() ||
            SYSTEM_OVERRIDE_PATTERN.matcher(normalizedInput).find() ||
            SYSTEM_MODE_PATTERN.matcher(normalizedInput).find()) {
            throw new GuardException("System manipulation attempt detected in user prompt");
        }

        // Check for complex prompt injection patterns
        if (containsComplexPromptInjection(input)) {
            throw new GuardException("Complex prompt injection detected in user prompt");
        }
    }

    /**
     * Checks for script injection attempts including HTML, JavaScript, and JSON injection.
     *
     * @param input the input string to check
     * @throws GuardException if script injection is detected
     */
    private static void checkForScriptInjection(String input) {
        if (!StringUtils.hasText(input)) {
            return;
        }

        String normalizedInput = input.toLowerCase().trim();

        // Check for HTML script tags and dangerous elements
        if (containsAnyTag(normalizedInput, HTML_SCRIPT_TAG_PATTERNS)) {
            throw new GuardException("HTML script injection detected in user prompt");
        }

        // Check for JavaScript patterns using separate patterns
        if (JAVASCRIPT_URI_PATTERN.matcher(normalizedInput).find() ||
            JAVASCRIPT_FUNCTIONS_PATTERN.matcher(normalizedInput).find() ||
            XMLHTTPREQUEST_PATTERN.matcher(normalizedInput).find() ||
            DOM_ACCESS_PATTERN.matcher(normalizedInput).find() ||
            containsTemplateLiteral(normalizedInput)) {
            throw new GuardException("JavaScript injection detected in user prompt");
        }

        // Check for CSS patterns using the new safer patterns
        if (CSS_IMPORT_PATTERN.matcher(normalizedInput).find() ||
            CSS_URL_JAVASCRIPT_PATTERN.matcher(normalizedInput).find() ||
            CSS_EXPRESSION_PATTERN.matcher(normalizedInput).find() ||
            CSS_MOZ_BINDING_PATTERN.matcher(normalizedInput).find() ||
            CSS_BEHAVIOR_PATTERN.matcher(normalizedInput).find() ||
            CSS_KEYFRAMES_PATTERN.matcher(normalizedInput).find() ||
            CSS_FONT_FACE_PATTERN.matcher(normalizedInput).find() ||
            CSS_CONTENT_URL_PATTERN.matcher(normalizedInput).find()) {
            throw new GuardException("CSS injection detected in user prompt");
        }

        // Check for dangerous attributes using safer string-based detection
        if (containsAnyPattern(normalizedInput, EVENT_HANDLER_PATTERNS) ||
            containsAnyPattern(normalizedInput, JAVASCRIPT_URI_ATTRIBUTE_PATTERNS)) {
            throw new GuardException("Event handler attribute detected in user prompt");
        }


        // Check for dangerous tags using safer string-based detection
        if (containsAnyTag(normalizedInput, SCRIPT_TAG_PATTERNS)) {
            throw new GuardException("Script-related tag detected in user prompt");
        }

        if (containsAnyTag(normalizedInput, STYLE_TAG_PATTERNS)) {
            throw new GuardException("Style-related tag detected in user prompt");
        }

        if (containsAnyTag(normalizedInput, FORM_TAG_PATTERNS)) {
            throw new GuardException("Form-related tag detected in user prompt");
        }

        if (containsAnyTag(normalizedInput, MEDIA_TAG_PATTERNS)) {
            throw new GuardException("Media-related tag detected in user prompt");
        }

        // Check for JSON injection patterns using safer string detection
        if (containsJSONInjection(normalizedInput)) {
            throw new GuardException("JSON injection detected in user prompt");
        }

        // Check for XSS patterns using separate patterns
        if (XSS_SCRIPT_TAGS_PATTERN.matcher(normalizedInput).find() ||
            XSS_PROTOCOLS_PATTERN.matcher(normalizedInput).find() ||
            XSS_LOAD_EVENT_PATTERN.matcher(normalizedInput).find() ||
            XSS_MOUSE_CLICK_PATTERN.matcher(normalizedInput).find() ||
            XSS_MOUSE_MOVEMENT_PATTERN.matcher(normalizedInput).find() ||
            XSS_MOUSE_BUTTON_PATTERN.matcher(normalizedInput).find() ||
            XSS_KEYBOARD_EVENT_PATTERN.matcher(normalizedInput).find() ||
            XSS_FORM_EVENT_PATTERN.matcher(normalizedInput).find() ||
            XSS_WINDOW_EVENT_PATTERN.matcher(normalizedInput).find()) {
            throw new GuardException("Cross-site scripting (XSS) attempt detected in user prompt");
        }
    }

    /**
     * Checks for SQL injection attempts.
     *
     * @param input the input string to check
     * @throws GuardException if SQL injection is detected
     */
    private static void checkForSqlInjection(String input) {
        if (!StringUtils.hasText(input)) {
            return;
        }

        String normalizedInput = input.toLowerCase().trim();

        // 1. Match known SQL patterns using separate patterns
        if (SQL_SELECT_PATTERN.matcher(normalizedInput).find() ||
            SQL_DML_PATTERN.matcher(normalizedInput).find() ||
            SQL_DDL_PATTERN.matcher(normalizedInput).find() ||
            SQL_CLAUSES_PATTERN.matcher(normalizedInput).find() ||
            SQL_FUNCTIONS_PATTERN.matcher(normalizedInput).find() ||
            SQL_CONTROL_PATTERN.matcher(normalizedInput).find() ||
            SQL_SYSTEM_PATTERN.matcher(normalizedInput).find()) {
            throw new GuardException("SQL keyword-based injection detected in user prompt");
        }

        // 2. Match SQL operators and payload patterns
        if (SQL_COMMENTS_PATTERN.matcher(normalizedInput).find() ||
            SQL_LOGICAL_OPERATORS_PATTERN.matcher(normalizedInput).find() ||
            SQL_EQUALITY_OPERATORS_PATTERN.matcher(normalizedInput).find() ||
            SQL_ALWAYS_TRUE_PATTERN.matcher(normalizedInput).find() ||
            SQL_OR_ALWAYS_TRUE_PATTERN.matcher(normalizedInput).find() ||
            SQL_UNION_PATTERN.matcher(normalizedInput).find()) {
            throw new GuardException("SQL operator-based injection attempt detected");
        }

        // 3. Match suspicious use of WHERE clause
        if (SQL_WHERE_EQUALITY_PATTERN.matcher(normalizedInput).find() ||
            SQL_WHERE_COMPARISON_PATTERN.matcher(normalizedInput).find() ||
            SQL_WHERE_LIKE_IN_PATTERN.matcher(normalizedInput).find() ||
            SQL_WHERE_NUMERIC_PATTERN.matcher(normalizedInput).find()) {
            throw new GuardException("Suspicious WHERE clause detected in user prompt");
        }
    }


    /**
     * Checks for suspicious words from a predefined dictionary with context awareness.
     * This method is designed to be more lenient for legitimate HRMS queries.
     *
     * @param input the input string to check
     * @throws GuardException if suspicious words are detected in malicious context
     */
    private static void checkForSuspiciousWords(String input) {
        if (!StringUtils.hasText(input)) {
            return;
        }

        // Skip suspicious word check for legitimate HR-related queries
        if (isLegitimateHRQuery(input)) {
            log.debug("Input appears to be legitimate HR query, skipping suspicious word check");
            return;
        }

        Set<String> suspiciousWordsSet = getSuspiciousWords();
        String[] words = input.toLowerCase().split("\\s+");

        for (String word : words) {
            // Clean the word of punctuation for better matching
            String cleanWord = word.replaceAll("[^a-zA-Z0-9]", "");
            if (suspiciousWordsSet.contains(cleanWord)||isSuspiciousContext(input, cleanWord)) {
                    throw new GuardException("Suspicious word detected in user prompt: " + cleanWord);
            }
        }
    }

    /**
     * Checks for anti-Islamic or anti-Saudi content patterns, including attempts
     * to generate negative content disguised as academic or research purposes.
     *
     * @param input the input string to check
     * @throws GuardException if anti-Islamic/anti-Saudi content is detected
     */
    private static void checkForAntiIslamicContent(String input) {
        if (!StringUtils.hasText(input)) {
            return;
        }

        String lowerInput = input.toLowerCase().trim();

        // Negative action words that could be used against protected terms
        String[] negativeActions = {
                NEGATIVE_TERM, "criticism", "criticize", "attack", "against", "anti", "oppose",
                "defame", "insult", "mock", "ridicule", "stereotype", "hate", "hatred",
                "offensive", "derogatory", "disparaging", "belittle", "undermine", "discredit",
                "slander", "vilify", "demonize", "stigmatize", "evil","weakness"

        };


        // Check for patterns: [negative action] + [protected term] + [academic pretext]
        boolean hasNegativeAction = false;

        for (String action : negativeActions) {
            if (lowerInput.contains(action)) {
                hasNegativeAction = true;
                break;
            }
        }

        // Block if trying to generate negative content about protected terms
        if (hasNegativeAction) {
            throw new GuardException("Content requesting negative information about Islamic/Saudi topics is not allowed");
        }

        // Block specific dangerous patterns
        if (containsDangerousAntiIslamicPattern(lowerInput)) {
            throw new GuardException("Anti-Islamic or anti-Saudi content pattern detected");
        }

    }

    /**
     * Checks for specific dangerous anti-Islamic patterns.
     *
     * @param input the normalized input string
     * @return true if dangerous patterns are found
     */
    private static boolean containsDangerousAntiIslamicPattern(String input) {
        // Specific dangerous patterns - using safer string operations instead of regex
        String lowerInput = input.toLowerCase();
        
        // Check for dangerous combinations using contains() instead of regex
        return (lowerInput.contains("show") && lowerInput.contains(NEGATIVE_TERM) && lowerInput.contains(SAUDI_TERM)) ||
                (lowerInput.contains("give") && lowerInput.contains("criticism") && lowerInput.contains(ISLAM_TERM)) ||
                (lowerInput.contains("list") && lowerInput.contains("problems") && lowerInput.contains(MUSLIM_TERM)) ||
                (lowerInput.contains("write") && lowerInput.contains("against") && lowerInput.contains(ISLAMIC_TERM)) ||
                (lowerInput.contains("generate") && lowerInput.contains(NEGATIVE_TERM) && lowerInput.contains(ARAB_TERM)) ||
                (lowerInput.contains("create") && lowerInput.contains("bad") && lowerInput.contains(SAUDI_TERM)) ||
                (lowerInput.contains("tell") && lowerInput.contains("wrong") && lowerInput.contains(ISLAM_TERM)) ||
                (lowerInput.contains("explain") && lowerInput.contains("issues") && lowerInput.contains(MUSLIM_TERM)) ||
                (lowerInput.contains("describe") && lowerInput.contains("flaws") && lowerInput.contains(ISLAMIC_TERM)) ||
                (lowerInput.contains("provide") && lowerInput.contains("attacks") && lowerInput.contains(SAUDI_TERM));
    }

    /**
     * Checks for complex prompt injection patterns that combine multiple techniques.
     *
     * @param input the input string to check
     * @return true if complex prompt injection is detected
     */
    private static boolean containsComplexPromptInjection(String input) {
        // Check for multiple suspicious patterns in combination
        int suspiciousPatternCount = 0;

        // Count various suspicious elements - using safer string operations
        if (input.contains("variable") && input.contains("=") && 
            containsVariablePattern(input)) {
            suspiciousPatternCount++;
        }

        if (input.contains("ResponseFormat") || input.contains("UserQuery")) {
            suspiciousPatternCount++;
        }

        if (input.contains("SYSTEM INSTRUCTION") || input.contains("</SYSTEM")) {
            suspiciousPatternCount++;
        }

        if (input.contains("divider") && input.contains(".-.-.-")) {
            suspiciousPatternCount++;
        }

        if (input.contains("personality:") || input.contains("tone:")) {
            suspiciousPatternCount++;
        }

        // If multiple suspicious patterns are found, it's likely a complex injection
        return suspiciousPatternCount >= COMPLEX_INJECTION_PATTERN_THRESHOLD;
    }

    /**
     * Checks if the input contains a variable pattern like "variable A =" safely without regex.
     *
     * @param input the input string to check
     * @return true if variable pattern is found
     */
    private static boolean containsVariablePattern(String input) {
        // Look for patterns like "variable A =", "variable B =", etc.
        String[] words = input.split("\\s+");
        for (int i = 0; i < words.length - VARIABLE_PATTERN_WORD_COUNT; i++) {
            if ("variable".equalsIgnoreCase(words[i]) && 
                words[i + 1].length() == 1 && 
                Character.isUpperCase(words[i + 1].charAt(0)) &&
                "=".equals(words[i + VARIABLE_PATTERN_WORD_COUNT])) {
                return true;
            }
        }
        return false;
    }

    /**
     * Checks if the input contains JSON injection patterns safely without regex.
     *
     * @param input the input string to check
     * @return true if JSON injection patterns are found
     */
    private static boolean containsJSONInjection(String input) {
        // Simple check for JSON object patterns
        boolean hasAllObjectPatterns = true;
        for (String pattern : JSON_SUSPICIOUS_PATTERNS) {
            if (!input.contains(pattern)) {
                hasAllObjectPatterns = false;
                break;
            }
        }
        
        if (hasAllObjectPatterns) {
            return true;
        }
        
        // Simple check for JSON array patterns
        boolean hasAllArrayPatterns = true;
        for (String pattern : JSON_ARRAY_SUSPICIOUS_PATTERNS) {
            if (!input.contains(pattern)) {
                hasAllArrayPatterns = false;
                break;
            }
        }
        
        return hasAllArrayPatterns;
    }

    /**
     * Checks if the input contains any of the specified HTML tags safely without regex.
     *
     * @param input the input string to check
     * @param tagPatterns array of tag patterns to check for
     * @return true if any tag pattern is found
     */
    private static boolean containsAnyTag(String input, String[] tagPatterns) {
        String lowerInput = input.toLowerCase();
        for (String tag : tagPatterns) {
            if (lowerInput.contains(tag.toLowerCase())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Checks if the input contains template literal patterns safely without regex.
     *
     * @param input the input string to check
     * @return true if template literal patterns are found
     */
    private static boolean containsTemplateLiteral(String input) {
        String lowerInput = input.toLowerCase();
        // Check if input contains backtick and any of the dangerous functions
        if (lowerInput.contains("`")) {
            return lowerInput.contains("alert(") || lowerInput.contains("confirm(") || 
                   lowerInput.contains("prompt(") || lowerInput.contains("eval(");
        }
        return false;
    }

    /**
     * Checks if the input contains any of the specified patterns safely without regex.
     *
     * @param input the input string to check
     * @param patterns array of patterns to check for
     * @return true if any pattern is found
     */
    private static boolean containsAnyPattern(String input, String[] patterns) {
        String lowerInput = input.toLowerCase();
        for (String pattern : patterns) {
            if (lowerInput.contains(pattern.toLowerCase())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Checks if the input appears to be a legitimate HR-related query.
     * Enhanced for Saudi Arabia deployment with cultural awareness.
     *
     * @param input the input string to check
     * @return true if it appears to be a legitimate HR query
     */
    private static boolean isLegitimateHRQuery(String input) {
        String lowerInput = input.toLowerCase();

        // Common HR-related keywords and patterns (English and Arabic context)
        String[] hrKeywords = {
                // Basic HR terms
                "employee", "staff", "team", "department", "manager", "supervisor",
                "salary", "payroll", "benefits", "vacation", "leave", "holiday",
                "performance", "review", "evaluation", "training", "development",
                "policy", "procedure", "handbook", "compliance", "hr",
                "recruitment", "hiring", "interview", "candidate", "position",
                "job", "role", "responsibility", "skill", "qualification",
                "attendance", "schedule", "shift", "overtime", "timesheet",
                "promotion", "transfer", "resignation", "termination",
                "onboarding", "orientation", "induction", "probation",

                // Saudi/Islamic workplace terms
                "ramadan", "eid", "hajj", "umrah", "prayer", "salah",
                "friday", "jummah", "break", "time", "religious", "observance",
                SAUDI_TERM, "kingdom", "ksa", "riyal", "ministry", "labor",
                "nitaqat", "saudization", "gosi", "social", "insurance",
                "visa", "iqama", "residence", "permit", "sponsor", "kafeel",
                "contract", "agreement", "terms", "conditions", "notice",
                "period", "gratuity", "end", "service", "benefits",

                // Professional terms
                "colleague", "coworker", "supervisor", "subordinate", "peer",
                "meeting", "conference", "workshop", "seminar", "presentation",
                "project", "task", "assignment", "deadline", "milestone",
                "budget", "cost", "expense", "reimbursement", "allowance",
                "medical", "insurance", "healthcare", "clinic", "hospital"
        };

        // Check if input contains HR-related keywords
        for (String keyword : hrKeywords) {
            if (lowerInput.contains(keyword)) {
                return true;
            }
        }

        // Check for common HR question patterns
        return (lowerInput.contains("who is") || lowerInput.contains("what is") || lowerInput.contains("how to") || 
                lowerInput.contains("when is") || lowerInput.contains("where is") || lowerInput.contains("why is") || 
                lowerInput.contains("can you") || lowerInput.contains("could you") || lowerInput.contains("please") || 
                lowerInput.contains("help")) &&
                lowerInput.length() < MAX_HR_QUERY_LENGTH || // Increased length for more complex legitimate queries
                (lowerInput.contains("information") || lowerInput.contains("details") || lowerInput.contains("status") || 
                 lowerInput.contains("update") || lowerInput.contains("report") || lowerInput.contains("list") || 
                 lowerInput.contains("summary"));
    }

    /**
     * Checks if a suspicious word appears in a truly suspicious context.
     *
     * @param input the full input string
     * @param suspiciousWord the suspicious word found
     * @return true if the word appears in suspicious context
     */
    private static boolean isSuspiciousContext(String input, String suspiciousWord) {
        String lowerInput = input.toLowerCase();

        // If it's a legitimate HR query, be more lenient
        if (isLegitimateHRQuery(input)) {
            return false;
        }

        // Check for context that indicates malicious intent
        String[] maliciousContexts = {
                "how to " + suspiciousWord,
                "teach me " + suspiciousWord,
                "show me " + suspiciousWord,
                "help me " + suspiciousWord,
                suspiciousWord + " tutorial",
                suspiciousWord + " guide",
                suspiciousWord + " instructions",
                "step by step " + suspiciousWord
        };

        for (String context : maliciousContexts) {
            if (lowerInput.contains(context)) {
                return true;
            }
        }

        // If the suspicious word appears with other technical/malicious terms
        return lowerInput.contains(suspiciousWord) &&
                (lowerInput.contains("bypass") || lowerInput.contains("exploit") ||
                        lowerInput.contains("attack") || lowerInput.contains("injection"));
    }

    private static final class SuspiciousWordsHolder {
        // Lazy-loaded suspicious words set
        private static final Set<String> suspiciousWords = loadSuspiciousWords();

        /**
         * Loads suspicious words from the classpath resource file.
         * This method is moved into the inner class to resolve SonarQube S3398 warning.
         *
         * @return set of suspicious words
         */
        private static Set<String> loadSuspiciousWords() {
            Set<String> words = new HashSet<>();

            try (InputStream inputStream = GuardUtils.class.getClassLoader()
                    .getResourceAsStream("suspicious-words.txt")) {

                if (inputStream == null) {
                    log.warn("Suspicious words file not found, using default set");
                    return getDefaultSuspiciousWords();
                }

                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

                    String line;
                    while ((line = reader.readLine()) != null) {
                        line = line.trim().toLowerCase();
                        if (!line.isEmpty() && !line.startsWith("#")) {
                            words.add(line);
                        }
                    }
                }

                log.info("Loaded {} suspicious words from configuration file", words.size());

            } catch (IOException e) {
                log.error("Error loading suspicious words file: {}", e.getMessage(), e);
                return getDefaultSuspiciousWords();
            }

            return words;
        }

        /**
         * Returns a default set of suspicious words if the configuration file is not available.
         * Enhanced for Saudi Arabia deployment with cultural sensitivity.
         * This method is moved into the inner class to resolve SonarQube S3398 warning.
         *
         * @return default set of suspicious words
         */
        private static Set<String> getDefaultSuspiciousWords() {
            Set<String> defaultWords = new HashSet<>();

            // Security-related terms
            defaultWords.add("hack");
            defaultWords.add("exploit");
            defaultWords.add("vulnerability");
            defaultWords.add("backdoor");
            defaultWords.add("malware");
            defaultWords.add("virus");
            defaultWords.add("trojan");
            defaultWords.add("rootkit");
            defaultWords.add("keylogger");
            defaultWords.add("phishing");
            defaultWords.add("spam");
            defaultWords.add("botnet");
            defaultWords.add("ddos");
            defaultWords.add("bruteforce");
            defaultWords.add("injection");
            defaultWords.add("bypass");
            defaultWords.add("privilege");
            defaultWords.add("escalation");
            defaultWords.add("payload");
            defaultWords.add("shellcode");

            // Inappropriate content
            defaultWords.add("illegal");
            defaultWords.add("fraud");
            defaultWords.add("scam");
            defaultWords.add("piracy");
            defaultWords.add("counterfeit");

            // Culturally sensitive terms for Saudi deployment
            defaultWords.add("pornography");
            defaultWords.add("gambling");
            defaultWords.add("alcohol");
            defaultWords.add("adultery");
            defaultWords.add("blasphemy");
            defaultWords.add("terrorism");
            defaultWords.add("extremism");
            defaultWords.add("drugs");
            defaultWords.add("narcotics");
            defaultWords.add("usury");
            defaultWords.add("riba");
            defaultWords.add("inappropriate");
            defaultWords.add("offensive");
            defaultWords.add("harassment");
            defaultWords.add("exploitation");

            // Chemical, Nuclear, and Biological terrorism terms
            defaultWords.add("chemical");
            defaultWords.add("toxic");
            defaultWords.add("poison");
            defaultWords.add("sarin");
            defaultWords.add("anthrax");
            defaultWords.add("ricin");
            defaultWords.add("nuclear");
            defaultWords.add("radioactive");
            defaultWords.add("uranium");
            defaultWords.add("plutonium");
            defaultWords.add("dirty");
            defaultWords.add("biological");
            defaultWords.add("bioweapon");
            defaultWords.add("pathogen");
            defaultWords.add("smallpox");
            defaultWords.add("ebola");
            defaultWords.add("weaponize");
            defaultWords.add("contamination");
            defaultWords.add("dispersal");
            defaultWords.add("genetic");

            // Anti-racism and hate speech protection
            defaultWords.add("racism");
            defaultWords.add("racist");
            defaultWords.add("discrimination");
            defaultWords.add("bigotry");
            defaultWords.add("xenophobia");
            defaultWords.add("islamophobia");
            defaultWords.add("antisemitism");
            defaultWords.add("supremacy");
            defaultWords.add("nazi");
            defaultWords.add("fascist");
            defaultWords.add("hate");
            defaultWords.add("slur");

            // Violence and harm protection
            defaultWords.add("violence");
            defaultWords.add("murder");
            defaultWords.add("kill");
            defaultWords.add("assault");
            defaultWords.add("torture");
            defaultWords.add("massacre");

            // Child safety protection
            defaultWords.add("pedophile");
            defaultWords.add("grooming");
            defaultWords.add("predator");

            // Self-harm protection
            defaultWords.add("suicide");
            defaultWords.add("selfharm");

            log.info("Using default suspicious words set with {} words (comprehensive protection)", defaultWords.size());
            return defaultWords;
        }
    }

    /**
     * Lazy-loads and returns the set of suspicious words from the configuration file.
     *
     * @return set of suspicious words
     */
    private static Set<String> getSuspiciousWords() {
        return SuspiciousWordsHolder.suspiciousWords;
    }



}
