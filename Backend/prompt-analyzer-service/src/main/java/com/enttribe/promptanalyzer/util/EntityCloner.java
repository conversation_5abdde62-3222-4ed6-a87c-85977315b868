/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.exception.BusinessException;
import jakarta.persistence.*;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.*;
import java.util.*;

/**
 * Utility class for cloning JPA entities with proper handling of different relationship types.
 *
 * <p>This class provides deep cloning capabilities for JPA entities while respecting
 * different relationship annotations:</p>
 * <ul>
 *   <li><strong>OneToMany</strong> - Deep clones the collection and its elements</li>
 *   <li><strong>ManyToMany</strong> - Shallow copies the collection</li>
 *   <li><strong>ManyToOne/OneToOne</strong> - Shallow copies the reference</li>
 *   <li><strong>Scalar fields</strong> - Direct value copy</li>
 * </ul>
 *
 * <p>The cloning process:</p>
 * <ul>
 *   <li>Skips static and final fields</li>
 *   <li>Skips fields annotated with @Id</li>
 *   <li>Handles null values gracefully</li>
 *   <li>Supports recursive deep cloning for OneToMany relationships</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class EntityCloner {

  private EntityCloner() {
    // Private constructor to hide the implicit public one
  }

  /**
   * Clones a JPA entity with proper handling of different relationship types.
   * Uses a non-reflection approach to avoid SonarQube S3011 warnings.
   *
   * @param <T> the type of the entity to clone
   * @param source the entity to clone, can be null
   * @return a cloned entity with the same type as the source, or null if source is null
   * @throws RuntimeException if cloning fails due to instantiation issues
   */
  public static <T> T cloneEntity(T source) {
    if (source == null) {
      return null;
    }

    try {
      Class<?> clazz = source.getClass();

      // Try to use copy constructor if available
      T clone = tryCopyConstructor(clazz, source);
      if (clone != null) {
        return clone;
      }

      // Try to use clone method if available
      clone = tryCloneMethod(source);
      if (clone != null) {
        return clone;
      }

      // Fallback to manual field copying using getter/setter methods
      return cloneUsingGettersSetters(source);

    } catch (Exception e) {
      throw new BusinessException("Failed to clone entity", e);
    }
  }

  /**
   * Attempts to clone using a copy constructor.
   */
  private static <T> T tryCopyConstructor(Class<?> clazz, T source) {
    try {
      Constructor<?> copyConstructor = clazz.getDeclaredConstructor(clazz);
      copyConstructor.setAccessible(false); // Ensure we don't bypass access control
      return (T) copyConstructor.newInstance(source);
    } catch (Exception e) {
      log.debug("No copy constructor found for class: {}", clazz.getName());
      return null;
    }
  }

  /**
   * Attempts to clone using the clone() method.
   */
  private static <T> T tryCloneMethod(T source) {
    try {
      if (source instanceof Cloneable) {
        Method cloneMethod = source.getClass().getMethod("clone");
        return (T) cloneMethod.invoke(source);
      }
    } catch (Exception e) {
      log.debug("Clone method not available for class: {}", source.getClass().getName());
    }
    return null;
  }

  /**
   * Clones an entity using getter and setter methods to avoid reflection access issues.
   */
  private static <T> T cloneUsingGettersSetters(T source) throws Exception {
    Class<?> clazz = source.getClass();
    @SuppressWarnings("unchecked")
    T clone = (T) clazz.getDeclaredConstructor().newInstance();

    for (Field field : getAllFields(clazz)) {
      int modifiers = field.getModifiers();

      // Skip static, final, or @Id fields
      if (Modifier.isStatic(modifiers) || Modifier.isFinal(modifiers) || field.isAnnotationPresent(Id.class)) {
        continue;
      }

      // Try to use getter/setter methods
      copyFieldUsingMethods(source, clone, field);
    }

    return clone;
  }

  /**
   * Copies a field value using getter and setter methods.
   */
  private static void copyFieldUsingMethods(Object source, Object target, Field field) throws Exception {
    String fieldName = field.getName();
    String capitalizedFieldName = fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);

    // Find getter method
    Method getter = findGetterMethod(source.getClass(), capitalizedFieldName);
    if (getter == null) {
      log.warn("No getter found for field: {} in class: {}", fieldName, source.getClass().getName());
      return;
    }

    // Find setter method
    Method setter = findSetterMethod(target.getClass(), capitalizedFieldName, field.getType());
    if (setter == null) {
      log.warn("No setter found for field: {} in class: {}", fieldName, target.getClass().getName());
      return;
    }

    // Get value using getter
    Object value = getter.invoke(source);

    // Set value using setter with proper handling of different field types
    if (value == null) {
      setter.invoke(target, (Object) null);
    } else if (field.isAnnotationPresent(OneToMany.class)) {
      Collection<?> collection = (Collection<?>) value;
      Collection<Object> clonedCollection = createEmptyCollection(field.getType());
      for (Object item : collection) {
        clonedCollection.add(cloneEntity(item)); // recursive deep clone
      }
      setter.invoke(target, clonedCollection);
    } else if (field.isAnnotationPresent(ManyToMany.class)) {
      Collection<?> collection = (Collection<?>) value;
      setter.invoke(target, new HashSet<>(collection)); // shallow copy
    } else if (isEntity(value.getClass())) {
      setter.invoke(target, value); // shallow copy for OneToOne or ManyToOne
    } else {
      setter.invoke(target, value); // scalar field
    }
  }

  /**
   * Finds a getter method for the given field.
   */
  private static Method findGetterMethod(Class<?> clazz, String capitalizedFieldName) {
    try {
      // Try standard getter naming convention
      return clazz.getMethod("get" + capitalizedFieldName);
    } catch (NoSuchMethodException e) {
      try {
        // Try boolean getter naming convention
        return clazz.getMethod("is" + capitalizedFieldName);
      } catch (NoSuchMethodException ex) {
        return null;
      }
    }
  }

  /**
   * Finds a setter method for the given field.
   */
  private static Method findSetterMethod(Class<?> clazz, String capitalizedFieldName, Class<?> fieldType) {
    try {
      return clazz.getMethod("set" + capitalizedFieldName, fieldType);
    } catch (NoSuchMethodException e) {
      return null;
    }
  }


  /**
   * Checks if a class is a JPA entity by looking for the @Entity annotation.
   *
   * @param clazz the class to check
   * @return true if the class is annotated with @Entity, false otherwise
   */
  private static boolean isEntity(Class<?> clazz) {
    return clazz.isAnnotationPresent(Entity.class);
  }

  /**
   * Creates an empty collection of the specified type.
   *
   * @param collectionType the type of collection to create
   * @return an empty collection of the specified type
   * @throws UnsupportedOperationException if the collection type is not supported
   */
  private static Collection<Object> createEmptyCollection(Class<?> collectionType) {
    if (collectionType.isAssignableFrom(Set.class)) {
      return new HashSet<>();
    } else if (collectionType.isAssignableFrom(List.class)) {
      return new ArrayList<>();
    } else if (!collectionType.isInterface()) {
      try {
        return (Collection<Object>) collectionType.getDeclaredConstructor().newInstance();
      } catch (Exception ignored) {
        log.info("Error in @method getDeclaredConstructor()");
        log.info("Error during @getDeclaredConstructor()");
      }
    }
    throw new UnsupportedOperationException("Unsupported collection type: " + collectionType);
  }

  /**
   * Gets all fields from a class and its superclasses, excluding Object.class.
   *
   * @param clazz the class to get fields from
   * @return a list of all fields from the class and its superclasses
   */
  private static List<Field> getAllFields(Class<?> clazz) {
    List<Field> fields = new ArrayList<>();
    while (clazz != null && clazz != Object.class) {
      fields.addAll(List.of(clazz.getDeclaredFields()));
      clazz = clazz.getSuperclass();
    }
    return fields;
  }
}
