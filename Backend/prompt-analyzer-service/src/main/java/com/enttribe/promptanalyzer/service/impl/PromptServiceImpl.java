/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.enttribe.product.security.spring.userdetails.CustomerInfo;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dao.LlmModelDao;
import com.enttribe.promptanalyzer.dao.PromptDao;
import com.enttribe.promptanalyzer.dto.assertion.AssertionTemplateDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptConvertorDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptDtoSdk;
import com.enttribe.promptanalyzer.dto.prompt.PromptRequestDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptVariableDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptVersionDetailsDto;
import com.enttribe.promptanalyzer.exception.AccessDeniedException;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.DuplicateResourceException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.LlmModel;
import com.enttribe.promptanalyzer.model.Message;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.service.PromptService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.*;
import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVRecord;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * Implementation of the {@link PromptService} interface. This class provides the actual business
 * logic for managing prompts and prompt variables for a specific application. It interacts with the
 * data access layer to fetch and modify prompt data.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PromptServiceImpl implements PromptService {

  private final PromptDao promptDao;
  private final CustomFilter customFilter;
  private final LlmModelDao llmModelDao;
  private final EntityManager entityManager;
  private final CustomerInfo customerInfo;

  private static final List<String> csvColumnHeader =
      List.of(
          "Application",
          "Name",
          "Category",
          "PromptId",
          "Temperature",
          "MaxToken",
          "Version",
          "JsonMode",
          "TopP",
          "Type",
          "LlmGuard",
          "AssertionTemplate",
          "DefaultFormat",
          "Tags",
          "Messages",
          "LLMModel",
          "Reasoning Effort");

  private static final String RESULT = "result";
  private static final String SUCCESS = "success";
  private static final String PUBLISH = "PUBLISH";

  private static final int MAX_IMPORT_RECORDS = 100;
  private static final int VARIABLE_GROUP_INDEX = 2;

  @Override
  public Map<String, String> savePrompt(PromptDto promptDto) {
    log.debug(
        "Inside @method savePrompt with @application: {}, @name: {}, @category: {}",
        promptDto.getApplication(),
        promptDto.getName(),
        promptDto.getCategory());
    Map<String, String> result = new HashMap<>();
    try {
      List<PromptVersionDetailsDto> versionsOfPrompt =
          getVersionsOfPrompt(
              promptDto.getApplication(),
              promptDto.getName(),
              promptDto.getCategory(),
              promptDto.getStatus());
      List<String> versionList =
          versionsOfPrompt.stream().map(PromptVersionDetailsDto::getVersion).toList();
      log.debug("Existing versions for prompt: {}", versionList);

      String nextVersion = getNextVersion(versionList, promptDto.getStatus());
      log.debug("Calculated next version: {}", nextVersion);

      Prompt prompt = new Prompt();
      prompt.setVersion(nextVersion);
      prompt.setPromptId(
          newPromptId(
              promptDto.getApplication(),
              promptDto.getName(),
              promptDto.getCategory(),
              nextVersion));
      prompt.setNanoId(NanoIdUtils.randomNanoId());
      PromptUtil.mapDtoToEntity(prompt, promptDto, llmModelDao);
      prompt.setCreatedTime(new Date());

      Prompt savedPrompt = promptDao.save(prompt);
      log.info(
          "Successfully created prompt with ID: {} and promptId: {}",
          savedPrompt.getId(),
          prompt.getPromptId());

      result.put(RESULT, SUCCESS);
      result.put("promptId", savedPrompt.getNanoId());
    } catch (ConstraintViolationException e) {
      ConstraintViolation<?> constraintViolation =
          e.getConstraintViolations().stream()
              .findFirst()
              .orElseThrow(
                  () -> new BusinessException("Failed to get constraint violation details"));
      log.error(
          "Validation failed while creating prompt: {} - {}",
          constraintViolation.getPropertyPath(),
          constraintViolation.getMessage(),
          e);
      throw new BusinessException(
          String.format(
              "Validation error: %s - %s",
              constraintViolation.getPropertyPath(), constraintViolation.getMessage()),
          e);
    } catch (Exception e) {
      if(e instanceof DataIntegrityViolationException) {
        throw new DuplicateResourceException("Prompt with same application, category, name, status, and version already exists"); // Let global handler return 403
      }
      log.error("Error in @method savePrompt : {}", e.getMessage(), e);
      throw new BusinessException("Failed to create prompt", e);
    }
    return result;
  }

  @Override
  public List<PromptVersionDetailsDto> getVersionsOfPrompt(
      String application, String name, String category, String status) {
    log.debug(
        "Inside @method getVersionsOfPrompt with @application: {}, @name: {}, @category: {}, @status: {}",
        application,
        name,
        category,
        status);
    List<PromptVersionDetailsDto> versionsOfPrompt =
        promptDao.getVersionsOfPrompt(application, name, category, status);
    return versionsOfPrompt.stream().filter(detail -> !detail.getVersion().equals("v-0")).toList();
  }

  @Override
  public Map<String, String> softDelete(String id) {
    log.debug("Inside @method softDelete with @id: {}", id);
    Map<String, String> result = new HashMap<>();

    Prompt prompt =
        promptDao
            .findByNanoId(id)
            .orElseThrow(() -> new ResourceNotFoundException(PromptConstants.PROMPT_NOT_FOUND));
    try {
      // Enforce creator-only soft delete
      customFilter.verifyCreator(customerInfo.getUserId(), prompt.getCreator());
      prompt.setDeleted(true);
      promptDao.save(prompt);
      log.info("Successfully soft deleted prompt with ID: {}", id);
      result.put(RESULT, SUCCESS);
    } catch (AccessDeniedException ade) {
      throw ade; // Let global handler return 403
    } catch (ResourceNotFoundException e) {
      log.error("Error in @method softDelete : {}", e.getMessage(), e);
      throw e;
    } catch (Exception e) {
      log.error("Error in @method softDelete : {}", e.getMessage(), e);
      throw new BusinessException("Failed to soft delete prompt", e);
    }
    return result;
  }

  @Override
  public PromptConvertorDto getPromptById(String id) {
    log.debug("Inside @method getPromptById. @param -> id : {}", id);
    try {
      Prompt prompt =
          promptDao
              .findByNanoId(id)
              .orElseThrow(() -> new ResourceNotFoundException(PromptConstants.PROMPT_NOT_FOUND));
      log.debug("Successfully retrieved prompt with ID: {}", id);
      return PromptConvertor.getPromptDto(prompt);
    } catch (ResourceNotFoundException e) {
      log.error("Error in @method getPromptById : {}", e.getMessage(), e);
      throw e;
    } catch (Exception e) {
      log.error("Error in @method getPromptById : {}", e.getMessage(), e);
      throw new BusinessException(PromptConstants.FAILED_TO_GET_PROMPT);
    }
  }

  @Override
  public PromptDtoSdk findPromptById(Integer id) {
    log.debug("inside @method findPromptById. @param -> id: {}", id);
    try {
      Prompt prompt =
          promptDao
              .findById(id)
              .orElseThrow(() -> new ResourceNotFoundException(PromptConstants.PROMPT_NOT_FOUND));
      return SdkUtils.getPromptDto(prompt);
    } catch (ResourceNotFoundException e) {
      log.error("Error in @method findPromptById : {}", e.getMessage(), e);
      throw e;
    } catch (Exception e) {
      log.error("Error in @method findPromptById : {}", e.getMessage(), e);
      throw new BusinessException("Failed to search prompt", e);
    }
  }

  @Override
  public Map<String, Object> exists(PromptRequestDto promptRequestDto) {
    log.debug("Inside @method exists");
    try {
      boolean exists =
          promptDao.existsByNameAndApplicationAndCategoryAndStatus(
              promptRequestDto.getName(),
              promptRequestDto.getApplication(),
              promptRequestDto.getCategory(),
              promptRequestDto.getStatus());
      return Map.of(RESULT, exists);
    } catch (Exception e) {
      log.error("Error in @method exists : {}", e.getMessage(), e);
      throw new BusinessException("Failed to load exists", e);
    }
  }

  @Override
  public Map<String, String> updateAssertionTemplate(AssertionTemplateDto assertionTemplateDto) {
    log.debug(
        "Inside @method updateAssertionTemplate with @promptId: {}",
        assertionTemplateDto.getPromptId());
    Map<String, String> response = new HashMap<>();

    try {
      Prompt existingPrompt =
          promptDao
              .findByNanoId(assertionTemplateDto.getPromptId())
              .orElseThrow(() -> new ResourceNotFoundException(PromptConstants.PROMPT_NOT_FOUND));

      if (!PUBLISH.equalsIgnoreCase(existingPrompt.getStatus())) {
        log.error(
            "Invalid prompt status for assertion template update. Expected: PUBLISH, Found: {}",
            existingPrompt.getStatus());
        throw new BusinessException(
            "Cannot update assertion template: Prompt status must be PUBLISH");
      }
      existingPrompt.setAssertionTemplate(assertionTemplateDto.getAssertionTemplate());
      existingPrompt.setModifiedTime(new Date());
      promptDao.save(existingPrompt);

      log.debug(
          "Successfully updated assertion template for prompt ID: {}",
          assertionTemplateDto.getPromptId());
      response.put(RESULT, SUCCESS);
    } catch (ResourceNotFoundException e) {
      log.error("Error in @method updateAssertionTemplate Exception : {}", e.getMessage(), e);
      throw e;
    } catch (BusinessException e) {
      log.error("Error in updateAssertionTemplate : {}", e.getMessage(), e);
      throw e;
    } catch (Exception e) {
      log.error("Error in @method updateAssertionTemplate : {}", e.getMessage(), e);
      throw new BusinessException("Failed to update assertion template", e);
    }
    return response;
  }

  @Override
  public Map<String, String> updatePrompt(PromptDto promptDto) {
    log.debug("Inside @method updatePrompt with @id: {}", promptDto.getId());
    Map<String, String> result = new HashMap<>();
    try {
      Prompt existingPrompt =
          promptDao
              .findByNanoId(promptDto.getId())
              .orElseThrow(() -> new ResourceNotFoundException(PromptConstants.PROMPT_NOT_FOUND));

      String status = existingPrompt.getStatus();
      if (status.equalsIgnoreCase("DRAFT") && promptDto.getStatus().equalsIgnoreCase(PUBLISH)) {
        List<PromptVersionDetailsDto> versionsOfPrompt =
            getVersionsOfPrompt(
                promptDto.getApplication(),
                promptDto.getName(),
                promptDto.getCategory(),
                promptDto.getStatus());
        List<String> versionList =
            versionsOfPrompt.stream().map(PromptVersionDetailsDto::getVersion).toList();
        log.debug("versions of prompt : {}", versionList);
        String nextVersion = getNextVersion(versionList, promptDto.getStatus());
        log.debug("next version : {}", nextVersion);
        existingPrompt.setVersion(nextVersion);
        existingPrompt.setPromptId(
            newPromptId(
                promptDto.getApplication(),
                promptDto.getName(),
                promptDto.getCategory(),
                nextVersion));
      }
      PromptUtil.mapDtoToEntity(existingPrompt, promptDto, llmModelDao);
      existingPrompt.setModifiedTime(new Date());

      promptDao.save(existingPrompt);
      result.put(RESULT, SUCCESS);
    } catch (ConstraintViolationException e) {
      ConstraintViolation<?> constraintViolation =
          e.getConstraintViolations().stream().findFirst().orElse(null);
      log.error("ConstraintViolationException while updating prompt", e);
      throw new BusinessException(
          String.format(
              "%s %s", constraintViolation.getPropertyPath(), constraintViolation.getMessage()),
          e);
    } catch (Exception e) {
      if(e instanceof DataIntegrityViolationException) {
        throw new DuplicateResourceException("Prompt with same application, category, name, status, and version already exists"); // Let global handler return 403
      }
      log.error("Error in @method updatePrompt : {}", e.getMessage(), e);
      throw new BusinessException("Unable to update prompt", e);
    }
    return result;
  }

  @Override
  public List<PromptConvertorDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.debug("Inside @method search");
    try {
      List<Prompt> prompts =
          customFilter.searchByFilter(Prompt.class, filter, orderBy, orderType, offset, size);
      return PromptConvertor.getPromptDtoList(prompts);
    } catch (Exception e) {
      log.error("Error in @method search : {}", e.getMessage(), e);
      throw new BusinessException("Failed to search prompts.", e);
    }
  }

  @Override
  public Long count(String filter) {
    log.debug("Inside @method count");
    try {
      return customFilter.countByFilter(Prompt.class, filter);
    } catch (Exception e) {
      log.error("Error in @method count : {}", e.getMessage(), e);
      throw new BusinessException("Failed to count prompts.", e);
    }
  }

  private String newPromptId(String application, String name, String category, String version) {
    log.debug(
        "Inside @method newPromptId with @application: {}, @name: {}, @category: {}, @version: {}",
        application,
        name,
        category,
        version);
    try {
      String promptId =
          (application + "-" + category + "-" + name + "-" + version).trim().replace(" ", "_");
      log.debug("Generated new prompt ID: {}", promptId);
      return promptId;
    } catch (Exception e) {
      log.error("Error in @method newPromptId : {}", e.getMessage(), e);
      throw new BusinessException("Failed to generate new prompt ID.", e);
    }
  }

  private String getNextVersion(List<String> versions, String status) {
    log.debug("Inside @method getNextVersion with @status: {}", status);
    try {
      if (status.equalsIgnoreCase("DRAFT")) {
        return "v-0";
      }
      if (versions == null || versions.isEmpty()) {
        // Default to v-0 if no versions are provided
        return "v-1";
      }

      int maxVersion =
          versions.stream()
              .filter(
                  // Only consider valid "v-<number>" strings
                  version -> version.matches("v-\\d+"))
              // Remove the prefix "v-"
              .map(version -> version.replace("v-", ""))
              // Parse the numeric part
              .mapToInt(Integer::parseInt)
              // Find the max value
              .max()
              // Default to 0 if no valid versions
              .orElse(1);
      // Increment and return the next version
      return "v-" + (maxVersion + 1);
    } catch (Exception e) {
      log.error("Error in @method getNextVersion : {}", e.getMessage(), e);
      throw new BusinessException("Failed to get next version.", e);
    }
  }

  @Override
  public List<PromptDtoSdk> getPromptByApplication(String appName) {
    log.debug("Inside @method getPromptByApplication with @appName: {}", appName);
    if (appName == null || appName.trim().isEmpty()) {
      throw new IllegalArgumentException("Application name cannot be null or empty.");
    }
    try {
      List<Prompt> prompts = promptDao.getPromptByApplication(appName);
      return SdkUtils.getPromptDtoList(prompts);
    } catch (Exception e) {
      log.error("Error in @method getPromptByApplication : {}", e.getMessage(), e);
      throw new BusinessException(PromptConstants.UNABLE_TO_GET_PROMPT);
    }
  }

  @Override
  public List<String> getDistinctApplications(String applicationName) {
    log.debug("Inside @method getDistinctApplications with @applicationName: {}", applicationName);
    return promptDao.getDistinctApplications(applicationName);
  }

  @Override
  public List<String> getDistinctCategoriesByApp(String applicationName) {
    log.debug(
        "Inside @method getDistinctCategoriesByApp with @applicationName: {}", applicationName);
    return promptDao.getDistinctCategoriesByApp(applicationName);
  }

  @Override
  public List<Prompt> filter(Map<String, Object> filterMap) {
    log.debug("Inside @method filter");
    try {
      CriteriaBuilder cb = entityManager.getCriteriaBuilder();
      CriteriaQuery<Prompt> query = cb.createQuery(Prompt.class);
      Root<Prompt> root = query.from(Prompt.class);

      List<Predicate> predicates = new ArrayList<>();

      // Build predicates based on filterMap keys
      for (Map.Entry<String, Object> entry : filterMap.entrySet()) {
        String key = entry.getKey();
        Object value = entry.getValue();

        // Assuming all filters are for equality
        if (value != null) {
          predicates.add(cb.equal(root.get(key), value));
        }
      }

      query.select(root).where(predicates.toArray(new Predicate[0]));

      // Execute the query
      return entityManager.createQuery(query).getResultList();
    } catch (Exception e) {
      log.error("Error in @method filter : {}", e.getMessage(), e);
      throw new BusinessException("Failed to filter prompts.", e);
    }
  }

  @Override
  public List<Prompt> searchV1(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.debug("Inside @method searchV1");
    try {
      return customFilter.searchByFilter(Prompt.class, filter, orderBy, orderType, offset, size);
    } catch (Exception e) {
      log.error("Error in @method searchV1 : {}", e.getMessage(), e);
      throw new BusinessException("Failed to search prompts (V1).", e);
    }
  }

  @Override
  public List<Map<String, String>> getPromptBasicDetailByApplication(String applicationName) {
    log.debug(
        "Inside @method getPromptBasicDetailByApplication with @applicationName: {}",
        applicationName);
    try {
      List<Map<String, String>> promptDetails = new ArrayList<>();
      List<Object[]> promptBasicDetails =
          promptDao.getPromptBasicDetailByApplication(applicationName);
      for (Object[] result : promptBasicDetails) {
        Map<String, String> response = new HashMap<>();
        String name = (String) result[0];
        String promptId = (String) result[1];
        response.put("name", name);
        response.put("promptId", promptId);
        promptDetails.add(response);
      }
      return promptDetails;
    } catch (Exception e) {
      log.error("Error in @method getPromptBasicDetailByApplication : {}", e.getMessage(), e);
      throw new BusinessException("Failed to get prompt basic details.", e);
    }
  }

  @Override
  public ResponseEntity<Resource> exportPrompt(String appName) {
    log.debug("Inside @method exportPrompt with @appName: {}", appName);
    try {
      List<Prompt> promptList = promptDao.getPromptByApplication(appName);
      return getResponse(appName, promptList);
    } catch (Exception e) {
      log.error("Error in @method exportPrompt : {}", e.getMessage(), e);
      throw new BusinessException("Failed to export prompt.", e);
    }
  }

  @Override
  public ResponseEntity<Resource> exportPromptsByIds(List<String> promptIds) {
    log.debug("Inside @method exportPromptsByIds");
    try {
      List<Prompt> promptList = promptDao.findAllByNanoIds(promptIds);
      return getResponse("prompts", promptList);
    } catch (Exception e) {
      log.error("Error in @method exportPromptsByIds : {}", e.getMessage(), e);
      throw new BusinessException("Failed to export prompts by IDs.", e);
    }
  }

  @Override
  public PromptDto fetchPromptById(String id) {
    log.debug("Inside @method fetchPromptById with @id: {}", id);
    try {
      return promptDao
          .findByNanoId(id)
          .map(PromptConvertor::mapToPromptDto)
          .orElseThrow(() -> new ResourceNotFoundException(PromptConstants.PROMPT_NOT_FOUND));

    } catch (ResourceNotFoundException e) {
      log.error("Error in @method fetchPromptById : {}", e.getMessage(), e);
      throw e;
    } catch (Exception e) {
      log.error("Unexpected error while getting prompt ", e);
      throw new BusinessException(PromptConstants.UNABLE_TO_GET_PROMPT);
    }
  }

  @Override
  public PromptDto getPromptByName(String promptName) {
    log.debug("Inside @method getPromptByName with @promptName: {}", promptName);
    try {
      return Optional.ofNullable(promptDao.findByName(promptName))
          .map(PromptConvertor::mapToPromptDto)
          .orElseThrow(() -> new ResourceNotFoundException(PromptConstants.PROMPT_NOT_FOUND));

    } catch (ResourceNotFoundException e) {
      log.error("Error in @method getPromptByName : {}", e.getMessage(), e);
      throw e;
    } catch (Exception e) {
      log.error("Error in @method getPromptByName : {}", e.getMessage(), e);
      throw new BusinessException(PromptConstants.UNABLE_TO_GET_PROMPT);
    }
  }

  @Override
  public PromptDtoSdk getPromptByIdV1(String id) {
    log.debug("Inside @method getPromptByIdV1 with @id: {}", id);
    try {
      Prompt prompt =
          promptDao
              .findByNanoId(id)
              .orElseThrow(() -> new ResourceNotFoundException(PromptConstants.PROMPT_NOT_FOUND));

      return SdkUtils.getPromptDto(prompt);
    } catch (ResourceNotFoundException e) {
      log.error("Error in @method getPromptByIdV1 : {}", e.getMessage(), e);
      throw e;
    } catch (Exception e) {
      log.error("Error in @method getPromptByIdV1 : {}", e.getMessage(), e);
      throw new BusinessException(PromptConstants.UNABLE_TO_GET_PROMPT);
    }
  }

  @Override
  public PromptConvertorDto getPromptByPromptId(String promptId) {
    log.debug("Inside @method getPromptByPromptId with @promptId: {}", promptId);
    if (promptId == null) {
      log.error("promptId is required to proceed");
      throw new IllegalArgumentException("promptId is required to proceed");
    }
    try {
      Prompt prompt =
          Optional.ofNullable(promptDao.findByPromptId(promptId))
              .orElseThrow(() -> new ResourceNotFoundException(PromptConstants.PROMPT_NOT_FOUND));
      log.debug("Successfully retrieved prompt with promptId: {}", promptId);
      return PromptConvertor.getPromptDto(prompt);

    } catch (ResourceNotFoundException e) {
      log.error("Error in @method getPromptByPromptId : {}", e.getMessage(), e);
      throw e;
    } catch (Exception e) {
      log.error("Error in @method getPromptByPromptId : {}", e.getMessage(), e);
      throw new BusinessException("Failed to fetch prompt", e);
    }
  }

  private static ResponseEntity<Resource> getResponse(String fileName, List<Prompt> promptList) {
    log.debug("Inside @method getResponse with @fileName: {}", fileName);
    try {
      List<Function<Prompt, Object>> fieldExtractors = getPromptFieldExtractors();
      log.debug("Going to export prompts as CSV");
      return CSVUtils.exportCSV(promptList, csvColumnHeader, fileName, fieldExtractors);
    } catch (Exception e) {
      log.error("Error in @method getResponse : {}", e.getMessage(), e);
      throw new BusinessException("Failed to export prompt", e);
    }
  }

  private static List<Function<Prompt, Object>> getPromptFieldExtractors() {
    log.debug("Inside @method getPromptFieldExtractors");
    return List.of(
        Prompt::getApplication,
        Prompt::getName,
        Prompt::getCategory,
        Prompt::getPromptId,
        Prompt::getTemperature,
        Prompt::getMaxToken,
        Prompt::getVersion,
        Prompt::getJsonMode,
        Prompt::getTopP,
        Prompt::getType,
        Prompt::getLlmGuard,
        prompt -> prompt.getAssertionTemplate() == null ? "NULL" : prompt.getAssertionTemplate(),
        prompt -> prompt.getDefaultFormat() == null ? "NULL" : prompt.getDefaultFormat(),
        prompt -> prompt.getTag() == null ? "NULL" : prompt.getTag(),
        prompt -> {
          try {
            return JsonUtils.convertToJSON(prompt.getMessages());
          } catch (Exception e) {
            log.error(
                "Error in @method getPromptFieldExtractors (messages) : {}", e.getMessage(), e);
            return "{}";
          }
        },
        prompt -> {
          var llmModel = prompt.getLlmModel();
          return llmModel == null ? "NULL" : llmModel.getProvider() + "/" + llmModel.getModel();
        },
        prompt -> prompt.getReasoningEffort() == null ? "NULL" : prompt.getReasoningEffort());
  }

  @Override
  public ResponseEntity<Resource> importPrompt(MultipartFile file) {
    log.debug("Inside @method importPrompt with @file: {}", file.getOriginalFilename());
    int maxRecords = MAX_IMPORT_RECORDS;
    try {
      Function<CSVRecord, Prompt> recordTransformer =
          csvRecord -> {
            try {
              Prompt prompt = new Prompt();
              prompt.setApplication(csvRecord.get("Application"));
              prompt.setName(csvRecord.get("Name"));
              prompt.setCategory(csvRecord.get("Category"));
              prompt.setPromptId(csvRecord.get("PromptId"));

              String temperatureValue = csvRecord.get("Temperature");
              prompt.setTemperature(Double.valueOf(temperatureValue));

              String maxTokenValue = csvRecord.get("MaxToken");
              prompt.setMaxToken(Integer.valueOf(maxTokenValue));

              prompt.setVersion(csvRecord.get("Version"));

              String jsonModeValue = csvRecord.get("JsonMode");
              prompt.setJsonMode(Boolean.valueOf(jsonModeValue));

              String topPValue = csvRecord.get("TopP");
              prompt.setTopP(Double.valueOf(topPValue));

              prompt.setType(csvRecord.get("Type"));

              String llmGuardValue = csvRecord.get("LlmGuard");
              prompt.setLlmGuard(Boolean.valueOf(llmGuardValue));

              prompt.setAssertionTemplate(parseNullable(csvRecord.get("AssertionTemplate")));
              prompt.setDefaultFormat(parseNullable(csvRecord.get("DefaultFormat")));
              prompt.setTag(parseNullable(csvRecord.get("Tags")));

              List<Message> messages =
                  JsonUtils.convertJsonToList(csvRecord.get("Messages"), Message.class);
              prompt.setMessages(messages);
              for (Message message : messages) {
                message.setPrompt(prompt);
              }

              String llmModelData = csvRecord.get("LLMModel");
              if (llmModelData != null && !llmModelData.isEmpty()) {
                String[] llmModelParts = llmModelData.split("/");
                String provider = llmModelParts[0];
                String model =
                    String.join("/", Arrays.copyOfRange(llmModelParts, 1, llmModelParts.length));
                LlmModel llmModel = llmModelDao.findByModelAndProvider(model, provider, "chat");
                prompt.setLlmModel(llmModel);
              }
              if (prompt.getLlmModel() == null) {
                log.warn("LLM model not found for provider/model: {}", llmModelData);
                throw new ResourceNotFoundException(
                    "Unable to determine LLM model for: " + llmModelData);
              }
              prompt.setReasoningEffort(parseNullable(csvRecord.get("Reasoning Effort")));
              return prompt;
            } catch (Exception e) {
              log.error("Error processing CSV record: {}", e.getMessage(), e);
              throw new BusinessException("Failed to process CSV record");
            }
          };

      String exportedFileName = file.getOriginalFilename().replaceAll("\\.[^.]*$", "");
      log.info("Processing import for file: {}", exportedFileName);
      return CSVUtils.importCSV(
          file,
          csvColumnHeader,
          recordTransformer,
          this::saveOrUpdatePrompt,
          exportedFileName,
          maxRecords);
    } catch (IOException e) {
      log.error("Error in @method importPrompt (IO) : {}", e.getMessage(), e);
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body(new ByteArrayResource(("File processing error: " + e.getMessage()).getBytes()));
    } catch (BusinessException e) {
      log.error("Error in @method importPrompt (Business) : {}", e.getMessage(), e);
      return ResponseEntity.status(HttpStatus.BAD_REQUEST)
          .body(new ByteArrayResource(e.getMessage().getBytes()));
    } catch (Exception e) {
      log.error("Error in @method importPrompt : {}", e.getMessage(), e);
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body(new ByteArrayResource(("Unexpected error: " + e.getMessage()).getBytes()));
    }
  }

  public static String parseNullable(String value) {
    return value != null && value.equalsIgnoreCase("NULL") ? null : value;
  }

  @Override
  public PromptVariableDto getPromptVariablesById(String promptId) {
    log.debug("Inside @method getPromptVariablesById with @promptId: {}", promptId);
    try {
      Prompt prompt = promptDao.findByPromptId(promptId);
      if (prompt == null) {
        log.error("Error in @method getPromptVariablesById : Prompt not found");
        throw new BusinessException(PromptConstants.PROMPT_NOT_FOUND);
      }
      PromptVariableDto dto = extractPromptVariables(prompt);
      log.debug("Successfully fetched prompt variables");
      return dto;
    } catch (Exception e) {
      log.error("Error in @method getPromptVariablesById : {}", e.getMessage(), e);
      throw new BusinessException(PromptConstants.FAILED_TO_GET_PROMPT);
    }
  }

  @Override
  public List<PromptVariableDto> getPromptVariablesByAppName(String appName) {
    log.debug("Inside @method getPromptVariablesByAppName with @appName: {}", appName);
    try {
      List<Prompt> prompts = promptDao.getPromptByApplication(appName);
      log.debug("prompts count {}", prompts.size());
      List<PromptVariableDto> promptVariableDtos = new ArrayList<>();
      for (Prompt prompt : prompts) {
        PromptVariableDto dto = extractPromptVariables(prompt);
        promptVariableDtos.add(dto);
      }
      log.debug("Successfully fetched prompt variables for application: {}", appName);
      return promptVariableDtos;
    } catch (Exception e) {
      log.error("Error in @method getPromptVariablesByAppName : {}", e.getMessage(), e);
      throw new BusinessException(PromptConstants.FAILED_TO_GET_PROMPT);
    }
  }

  private PromptVariableDto extractPromptVariables(Prompt prompt) {
    log.debug("Inside @method extractPromptVariables");
    try {
      String promptName = prompt.getName();
      List<String> userPromptVariables = new ArrayList<>();
      List<String> systemPromptVariables = new ArrayList<>();
      List<String> assistantPromptVariables = new ArrayList<>();

      for (Message message : prompt.getMessages()) {
        String content = message.getContent();

        List<String> variables = extractVariables(content);

        if (message.getRole().equalsIgnoreCase("user")) {
          userPromptVariables.addAll(variables);
        } else if (message.getRole().equalsIgnoreCase("system")) {
          systemPromptVariables.addAll(variables);
        } else if (message.getRole().equalsIgnoreCase("assistant")) {
          assistantPromptVariables.addAll(variables);
        }
      }

      PromptVariableDto dto = new PromptVariableDto();
      dto.setPromptName(promptName);
      dto.setUserPrompt(userPromptVariables);
      dto.setSystemPrompt(systemPromptVariables);
      dto.setAssistantPrompt(assistantPromptVariables);
      log.debug("Successfully extracted prompt variables");
      return dto;
    } catch (Exception e) {
      log.error("Error in @method extractPromptVariables : {}", e.getMessage(), e);
      throw new BusinessException("Failed to extract prompt variables.", e);
    }
  }

  private static List<String> extractVariables(String content) {
    log.debug("Inside @method extractVariables");
    List<String> variables = new ArrayList<>();
    Pattern pattern =
        Pattern.compile("\\{\\{\\s*([^\\s{}\"]+?)\\s*\\}\\}|\\{\\s*([^\\s{}\"]+?)\\s*\\}");
    Matcher matcher = pattern.matcher(content);

    while (matcher.find()) {
      // Check if the match corresponds to the {{variable}} format
      if (matcher.group(1) != null) {
        variables.add(matcher.group(1));
      }
      // Check if the match corresponds to the {variable} format
      else if (matcher.group(VARIABLE_GROUP_INDEX) != null) {
        variables.add(matcher.group(VARIABLE_GROUP_INDEX));
      }
    }
    log.debug("Extracted variables: {}", variables);
    return variables;
  }

  private void saveOrUpdatePrompt(Prompt prompt) {
    log.debug("Inside @method saveOrUpdatePrompt with @promptId: {}", prompt.getPromptId());
    try {
      Prompt existingPrompt = promptDao.findByPromptId(prompt.getPromptId());

      if (existingPrompt != null) {
        mapPrompt(prompt, existingPrompt);
        existingPrompt.setModifiedTime(new Date());
        promptDao.save(existingPrompt);
        log.info("Updating existing prompt with ID: {}", prompt.getPromptId());
      } else {
        prompt.setStatus(PUBLISH);
        prompt.setModifiedTime(new Date());
        prompt.setCreatedTime(new Date());
        prompt.setDeleted(false);
        prompt.setNanoId(NanoIdUtils.randomNanoId());
        log.debug("Creating new prompt with name: {}", prompt.getName());
        promptDao.save(prompt);
      }
      log.debug("Prompt saved or updated successfully with ID: {}", prompt.getPromptId());
    } catch (Exception e) {
      log.error("Error in @method saveOrUpdatePrompt : {}", e.getMessage(), e);
      throw new BusinessException("Failed to save or update prompt.", e);
    }
  }

  private void mapPrompt(Prompt source, Prompt target) {
    log.debug("Inside @method mapPrompt");
    try {
      if (source.getApplication() != null) {
        target.setApplication(source.getApplication());
      }
      if (source.getName() != null) {
        target.setName(source.getName());
      }
      if (source.getCategory() != null) {
        target.setCategory(source.getCategory());
      }
      if (source.getTemperature() != null) {
        target.setTemperature(source.getTemperature());
      }
      if (source.getMaxToken() != null) {
        target.setMaxToken(source.getMaxToken());
      }
      if (source.getVersion() != null) {
        target.setVersion(source.getVersion());
      }
      if (source.getJsonMode() != null) {
        target.setJsonMode(source.getJsonMode());
      }
      if (source.getTopP() != null) {
        target.setTopP(source.getTopP());
      }
      if (source.getType() != null) {
        target.setType(source.getType());
      }
      if (source.getLlmGuard() != null) {
        target.setLlmGuard(source.getLlmGuard());
      }
      extractedMapPrompt(source, target);
      log.debug("Mapped target prompt: {}", target);
    } catch (Exception e) {
      log.error("Error in @method mapPrompt : {}", e.getMessage(), e);
      throw new BusinessException("Failed to map prompt.", e);
    }
  }

  private static void extractedMapPrompt(Prompt source, Prompt target) {
    log.debug("Inside @method extractedMapPrompt");
    try {
      if (source.getAssertionTemplate() != null) {
        target.setAssertionTemplate(source.getAssertionTemplate());
      }
      if (source.getDefaultFormat() != null) {
        target.setDefaultFormat(source.getDefaultFormat());
      }
      if (source.getTag() != null) {
        target.setTag(source.getTag());
      }
      if (source.getLlmModel() != null) {
        target.setLlmModel(source.getLlmModel());
      }
      List<Message> messages = source.getMessages();
      if (messages != null && !messages.isEmpty()) {
        target.getMessages().clear();
        target.getMessages().addAll(messages);
        for (Message message : messages) {
          message.setPrompt(target);
        }
      }
      log.debug("Successfully mapped prompt");
    } catch (Exception e) {
      log.error("Error in @method extractedMapPrompt : {}", e.getMessage(), e);
      throw new BusinessException("Failed to map prompt (extracted).", e);
    }
  }

  @Override
  public Map<String, String> updateTagById(String id, Map<String, String> tags) {
    log.debug("Inside @method updateTagById with @id: {}", id);
    try {
      Prompt prompt =
          promptDao
              .findByNanoId(id)
              .orElseThrow(() -> new ResourceNotFoundException(PromptConstants.PROMPT_NOT_FOUND));
      String newTag = tags.get("tags");
      prompt.setTag(newTag);
      promptDao.save(prompt);

      log.debug("Successfully updated tags for prompt ID: {}", id);
      return Map.of(RESULT, SUCCESS);
    } catch (ResourceNotFoundException e) {
      log.error("Error in @method updateTagById : {}", e.getMessage(), e);
      throw e;
    } catch (Exception e) {
      log.error("Error in @method updateTagById : {}", e.getMessage(), e);
      throw new BusinessException("Failed to update tags");
    }
  }
}
