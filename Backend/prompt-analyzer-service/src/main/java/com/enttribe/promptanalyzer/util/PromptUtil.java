/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.dao.LlmModelDao;
import com.enttribe.promptanalyzer.dto.prompt.PromptDto;
import com.enttribe.promptanalyzer.model.LlmModel;
import com.enttribe.promptanalyzer.model.Message;
import com.enttribe.promptanalyzer.model.Prompt;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

/**
 * Utility class for operations related to Prompt entities and DTOs.
 *
 * <p>This class provides static helper methods for mapping and manipulating Prompt-related objects.
 */
@Slf4j
public final class PromptUtil {

  private PromptUtil() {
    // Private constructor to prevent instantiation
  }

  /**
   * Maps the properties from a {@link PromptDto} to a {@link Prompt} entity, including associated
   * messages and model.
   *
   * <p>This method updates the given Prompt entity with values from the provided PromptDto,
   * including its messages and associated LlmModel. It also ensures that the Prompt's message
   * collection is properly updated and associated.
   *
   * @param prompt the Prompt entity to update
   * @param promptDto the PromptDto containing the new values
   * @param llmModelDao the DAO used to fetch the associated LlmModel
   * @throws IllegalArgumentException if the LlmModel cannot be found for the given model and
   *     provider
   */
  public static void mapDtoToEntity(Prompt prompt, PromptDto promptDto, LlmModelDao llmModelDao) {
    log.debug(
        "Mapping PromptDto to Prompt entity for application: {}, name: {}",
        promptDto.getApplication(),
        promptDto.getName());
    prompt.setApplication(promptDto.getApplication());
    prompt.setCategory(promptDto.getCategory());
    prompt.setStatus(promptDto.getStatus());
    prompt.setTemperature(promptDto.getTemperature());
    prompt.setMaxToken(promptDto.getMaxTokens());
    prompt.setTopP(promptDto.getTopP());
    prompt.setName(promptDto.getName());
    prompt.setAssertionTemplate(promptDto.getAssertionTemplate());
    prompt.setDefaultFormat(promptDto.getDefaultFormat());
    Boolean jsonMode = promptDto.getJsonMode();
    prompt.setJsonMode(!(jsonMode == null || !jsonMode));
    prompt.setLlmGuard(promptDto.getLlmGuard());
    prompt.setType(promptDto.getType());
    prompt.setTag(promptDto.getTags());
    prompt.setToolChoice(promptDto.getToolChoice());

    if (promptDto.getMessages() != null) {
      // Clear the existing collection
      prompt.getMessages().clear();

      // Add all messages from DTO to the existing collection
      for (Message message : promptDto.getMessages()) {
        message.setPrompt(prompt); // Associate the message with the prompt
        prompt.getMessages().add(message); // Add the message to the collection
      }
    } else {
      // If the incoming DTO has null messages, clear the existing collection
      prompt.getMessages().clear();
    }

    LlmModel llmModel =
        llmModelDao.findByModelAndProvider(promptDto.getModel(), promptDto.getProvider(), "chat");
    Assert.notNull(llmModel, "llm model is not provided");
    prompt.setLlmModel(llmModel);
    prompt.setReasoningEffort(promptDto.getReasoningEffort());
    prompt.setModifiedTime(new Date());
    log.debug("Mapped Prompt entity: {}", prompt);
  }
}
