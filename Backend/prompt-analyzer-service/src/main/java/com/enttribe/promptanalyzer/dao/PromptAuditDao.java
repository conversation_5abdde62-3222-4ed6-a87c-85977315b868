/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.PromptAudit;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

/**
 * Data Access Object interface for PromptAudit entity operations. Provides methods to interact with
 * prompt audit logs in the database, tracking the history and changes of prompts.
 *
 * <AUTHOR>
 * @version 1.0
 * @see PromptAudit
 * @see JpaRepository
 */
public interface PromptAuditDao extends JpaRepository<PromptAudit, Integer> {

  /**
   * Retrieves all prompt audit entries for a specific audit ID.
   *
   * @param auditId The unique identifier of the audit trail
   * @return List of PromptAudit entries matching the audit ID
   */
  @Query("SELECT p FROM PromptAudit p WHERE p.auditId = :auditId")
  List<PromptAudit> getPromptAuditListByAuditId(String auditId);

  /**
   * Retrieves all prompt audit entries for a specific prompt ID.
   *
   * @param promptId The unique identifier of the prompt
   * @return List of PromptAudit entries matching the prompt ID
   */
  @Query("SELECT p FROM PromptAudit p WHERE p.promptId = :promptId")
  List<PromptAudit> getPromptAuditListByPromptId(String promptId);
}
