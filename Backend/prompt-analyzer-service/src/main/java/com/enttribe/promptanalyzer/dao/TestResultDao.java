/*
 * Copyright (c) 2024 Vwaves Technologies Private Limited
 *
 * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.
 * Product: Aspose.Total for Java
 * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations
 * Order ID: 250610115636
 *
 * This source code is part of a proprietary software system and may not be
 * distributed, modified, or reused without explicit written permission from
 * Vwaves Technologies Private Limited.
 *
 * Contact: <EMAIL>
 */
package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.TestResult;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Data Access Object interface for TestResult entity operations. Provides methods to interact
 * with the TestResult table in the database for managing test execution results.
 *
 * <p>This interface extends JpaRepository to provide standard CRUD operations and includes
 * custom query methods for retrieving test results with their associated test case results.</p>
 *
 * @see TestResult
 * @see JpaRepository
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public interface TestResultDao extends JpaRepository<TestResult, Long> {

  /**
   * Finds a test result by its nanoId and eagerly loads its associated test case results.
   *
   * @param nanoId the nanoId to search for
   * @return an Optional containing the test result with loaded test case results if found, empty otherwise
   */
  @Query(
      "SELECT tr FROM TestResult tr LEFT JOIN FETCH tr.testCaseResults WHERE tr.nanoId = :nanoId")
  Optional<TestResult> findWithResultsByNanoId(@Param("nanoId") String nanoId);
}
