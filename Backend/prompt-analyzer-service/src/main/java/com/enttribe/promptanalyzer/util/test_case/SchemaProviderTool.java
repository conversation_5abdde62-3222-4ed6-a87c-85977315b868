/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util.test_case;

import com.enttribe.promptanalyzer.model.KnowledgeBase;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;

/**
 * Function implementation for providing database schema information through vector search.
 *
 * <p>This class implements a function that takes a table name as input and returns the
 * corresponding database schema by performing vector similarity search on stored schema
 * documents. It's designed to work with SQL-type knowledge bases where schema information
 * is stored as vectorized documents.</p>
 *
 * <p>The tool works by:</p>
 * <ul>
 *   <li>Taking a table name as input</li>
 *   <li>Creating a filter expression to search for documents with matching doc_id</li>
 *   <li>Performing vector similarity search on the knowledge base</li>
 *   <li>Extracting and returning the schema information from the found documents</li>
 * </ul>
 *
 * <p>This implementation is specifically designed for SQL knowledge bases and provides
 * quick access to database schema information through natural language queries.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 * @see VectorStore
 * @see KnowledgeBase
 * @see Document
 */
public class SchemaProviderTool
    implements Function<SchemaProviderTool.Request, SchemaProviderTool.Response> {

  private static final Logger log = LoggerFactory.getLogger(SchemaProviderTool.class);
  
  /** The vector store for performing similarity search operations. */
  private final VectorStore vectorStore;

  /**
   * Constructs a new SchemaProviderTool with the specified vector store and knowledge base.
   *
   * <p>This constructor initializes the tool with a vector store for performing
   * similarity searches and logs the metadata information from the knowledge base
   * for debugging purposes.</p>
   *
   * @param vectorStore the vector store for similarity search operations
   * @param knowledgeBase the knowledge base configuration containing metadata
   */
  public SchemaProviderTool(VectorStore vectorStore, KnowledgeBase knowledgeBase) {
    String metaData = knowledgeBase.getVectorMetaData();
    log.info("initializing SchemaProvider tool. metaData : {}", metaData);
    this.vectorStore = vectorStore;
  }

  /**
   * Processes a question and returns a response based on the knowledge base content. This method
   * performs a vector search using the provided question and returns the most relevant answer found
   * in the document store.
   *
   * @param request The request containing the question to be answered
   * @return A Response object containing the answer retrieved from the knowledge base
   */
  @Override
  public Response apply(Request request) {
    log.info("inside function KnowledgeBase. request : {}", request.tableName());
    String filterExpression = String.format("'doc_id' == '%s'", request.tableName());
    log.info("filterExpression for SchemaProviderTool : {}", filterExpression);
    SearchRequest searchRequest =
        SearchRequest.builder()
            .filterExpression(filterExpression)
            .query(request.tableName())
            .build();

    List<Document> documents = vectorStore.similaritySearch(searchRequest);
    String tableSchema =
        Optional.ofNullable(documents).orElse(List.of()).stream()
            .map(Document::getText)
            .collect(Collectors.joining());
    return new Response(tableSchema);
  }

  /**
   * Record representing a tableName request to the knowledge base.
   *
   * @param tableName The table name for which schema will be provided.
   */
  public record Request(
      @JsonProperty("The table name for which schema will be provided") String tableName) {}

  /**
   * Record representing the response from the knowledge base.
   *
   * @param result The answer retrieved from the knowledge base
   */
  public record Response(@JsonProperty("result") String result) {}
}
