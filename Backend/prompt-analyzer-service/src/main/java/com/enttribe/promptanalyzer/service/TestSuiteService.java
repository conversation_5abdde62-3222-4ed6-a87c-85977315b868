/*
 * Copyright (c) 2024 Vwaves Technologies Private Limited
 *
 * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.
 * Product: Aspose.Total for Java
 * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations
 * Order ID: 250610115636
 *
 * This source code is part of a proprietary software system and may not be
 * distributed, modified, or reused without explicit written permission from
 * Vwaves Technologies Private Limited.
 *
 * Contact: <EMAIL>
 */
package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.testsuite.TestSuiteRequestDto;
import com.enttribe.promptanalyzer.dto.testsuite.TestSuiteResponseDto;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

/**
 * Service interface for managing test suite operations and lifecycle.
 * 
 * <p>This interface provides comprehensive functionality for creating, updating, searching,
 * and managing test suites. It supports CRUD operations, bulk operations, and import/export
 * capabilities for test suite data.</p>
 *
 * <p>Key responsibilities:</p>
 * <ul>
 *   <li>Create and update test suites with validation</li>
 *   <li>Search and retrieve test suites with filtering and pagination</li>
 *   <li>Delete test suites by unique identifier</li>
 *   <li>Add agent test cases to existing test suites</li>
 *   <li>Export test cases from test suites</li>
 *   <li>Import test cases into test suites from files</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface TestSuiteService {

  /**
   * Creates a new test suite with the provided configuration.
   * 
   * @param requestDto the test suite request DTO containing creation parameters
   * @return a map containing the result status and any relevant response data
   */
  Map<String, String> create(TestSuiteRequestDto requestDto);

  /**
   * Updates an existing test suite with new configuration data.
   * 
   * @param requestDto the test suite request DTO containing update parameters
   * @return a map containing the result status and any relevant response data
   */
  Map<String, String> update(TestSuiteRequestDto requestDto);

  /**
   * Searches for test suites with pagination, filtering, and sorting capabilities.
   * 
   * @param filter optional filter criteria for searching test suites
   * @param offset the pagination offset (zero-based)
   * @param size the number of results to return per page
   * @param orderBy optional field name to sort by
   * @param orderType optional sort direction ("asc" or "desc")
   * @return a list of test suite response DTOs matching the criteria
   */
  List<TestSuiteResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType);

  /**
   * Counts the total number of test suites matching the filter criteria.
   * 
   * @param filter optional filter criteria for counting test suites
   * @return the total count of matching test suites
   */
  Long count(String filter);

  /**
   * Deletes a test suite by its unique nano identifier.
   * 
   * @param nanoId the unique nano identifier of the test suite to delete
   * @return a map containing the result status and any relevant response data
   */
  Map<String, String> deleteById(String nanoId);

  /**
   * Adds agent test cases to an existing test suite.
   * 
   * @param requestDto the test suite request DTO containing the test cases to add
   * @return a map containing the result status and any relevant response data
   */
  Map<String, String> addAgentTestCasesToSuite(TestSuiteRequestDto requestDto);

  /**
   * Exports test cases from a specific test suite as a downloadable resource.
   * 
   * @param suiteId the unique identifier of the test suite to export
   * @return a ResponseEntity containing the exported resource
   */
  ResponseEntity<Resource> exportTestCasesBySuiteId(String suiteId);

  /**
   * Imports test cases into a test suite from an uploaded file.
   * 
   * @param suiteId the unique identifier of the test suite to import into
   * @param file the multipart file containing the test cases to import
   * @return a ResponseEntity containing the import result
   * @throws IOException if an error occurs during file processing
   */
  ResponseEntity<Resource> importTestCasesToSuite(String suiteId, MultipartFile file)
      throws IOException;
}
