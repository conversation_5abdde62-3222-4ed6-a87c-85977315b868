/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.microintent.MicroIntentResponseDto;
import com.enttribe.promptanalyzer.dto.tool.SwaggerDto;
import com.enttribe.promptanalyzer.dto.tool.ToolConvertorDto;
import com.enttribe.promptanalyzer.dto.tool.ToolDto;
import com.enttribe.promptanalyzer.dto.tool.ToolDtoSdk;
import com.enttribe.promptanalyzer.dto.tool.ToolWorkflowDto;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

/**
 * Manages tool operations and integrations across the system. This service handles tool lifecycle
 * management including creation, updates, and search operations. It also provides functionality for
 * tool import/export and integration with agents.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface ToolService {

  /**
   * Creates a new tool in the system.
   *
   * @param toolDto the tool data transfer object
   * @return a map containing the result of the creation operation
   */
  Map<String, String> createTool(ToolDto toolDto);

  /**
   * Searches for tools based on a filter and pagination/sorting options.
   *
   * @param filter the search filter
   * @param offset the offset for pagination
   * @param size the page size
   * @param orderBy the field to order by
   * @param orderType the order type (asc/desc)
   * @return a list of tool convertor DTOs
   */
  List<ToolConvertorDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType);

  /**
   * Counts the number of tools matching a filter.
   *
   * @param filter the search filter
   * @return the count of matching tools
   */
  Long count(String filter);

  /**
   * Soft deletes a tool by its integer ID.
   *
   * @param id the tool ID
   * @return a map containing the result of the delete operation
   */
  Map<String, String> softDelete(String id);

  /**
   * Updates an existing tool.
   *
   * @param tool the tool DTO
   * @return a map containing the result of the update operation
   */
  Map<String, String> updateTool(ToolDto tool);

  /**
   * Generates tools from a Swagger definition.
   *
   * @param swaggerDto the Swagger DTO
   * @return a map containing the result of the generation operation
   */
  Map<String, Integer> generateTools(SwaggerDto swaggerDto);

  /**
   * Changes the status of a tool by its integer ID.
   *
   * @param id the tool ID
   * @param status the new status
   * @return a map containing the result of the status change
   */
  Map<String, String> changeToolStatus(String id, String status);

  /**
   * Generates tools from a workflow definition.
   *
   * @param toolWorkflowDto the tool workflow DTO
   */
  void generateToolsFromWorkflow(ToolWorkflowDto toolWorkflowDto);

  /**
   * Retrieves a tool by its integer ID as a ToolConvertorDto.
   *
   * @param id the tool ID
   * @return the tool convertor DTO
   */
  ToolConvertorDto getToolById(String id);

  /**
   * Retrieves tools by a list of integer IDs as ToolDtoSdk objects.
   *
   * @param ids the list of tool IDs
   * @return a list of tool DTO SDKs
   */
  List<ToolDtoSdk> getToolsByIds(List<Integer> ids);

  /**
   * Retrieves tools by a list of integer IDs as ToolConvertorDto objects.
   *
   * @param ids the list of tool IDs
   * @return a list of tool convertor DTOs
   */
  List<ToolConvertorDto> getToolsByIdsV1(List<String> ids);

  /**
   * Checks if the provided source code compiles successfully.
   *
   * @param sourceCode the source code to check
   * @param className the class name
   * @return a map with result true if compilation is successful, and false otherwise
   */
  Map<String, Object> checkCompilation(String sourceCode, String className);

  /**
   * Exports tools for a given application name.
   *
   * @param appName the application name
   * @return a ResponseEntity containing the exported resource
   */
  ResponseEntity<Resource> exportTool(String appName);

  /**
   * Imports tools from a file.
   *
   * @param file the multipart file containing tools
   * @return a ResponseEntity containing the import result
   */
  ResponseEntity<Resource> importTool(MultipartFile file);

  /**
   * Checks if a tool exists by name.
   *
   * @param toolName the tool name
   * @return a map indicating existence and related information
   */
  Map<String, Boolean> existsTool(String toolName);

  /**
   * Updates tags for a tool by its string ID.
   *
   * @param id the tool ID
   * @param tags the tags to update
   * @return a map containing the result of the update operation
   */
  Map<String, String> updateTagById(String id, Map<String, String> tags);

  /**
   * Retrieves the tool callback provider information.
   *
   * @return a list of hash maps containing callback provider details
   */
  List<HashMap<String, String>> getToolCallbackProvider();

  /**
   * Retrieves a tool by its name as a ToolDto.
   *
   * @param toolName the tool name
   * @return the tool DTO
   */
  ToolDto getToolByName(String toolName);

  /**
   * Retrieves the X101 tool list.
   *
   * @return a list of micro intent response DTOs
   */
  List<MicroIntentResponseDto> getX101ToolList();

  /**
   * Finds tools by their integer IDs.
   *
   * @param ids the list of tool IDs
   * @return a list of tool DTOs
   */
  List<ToolDto> findToolsByIds(List<Integer> ids);
}
