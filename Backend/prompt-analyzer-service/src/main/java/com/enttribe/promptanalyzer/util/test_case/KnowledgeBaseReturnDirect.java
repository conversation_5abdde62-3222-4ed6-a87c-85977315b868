/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util.test_case;

import com.enttribe.promptanalyzer.model.KnowledgeBase;
import com.enttribe.promptanalyzer.service.LlmApiService;
import com.enttribe.promptanalyzer.util.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;

/**
 * Function implementation for knowledge base querying with direct response generation.
 *
 * <p>This class implements a function that takes a question as input and returns a structured
 * response by searching through a knowledge base using vector similarity search. It combines
 * vector search capabilities with LLM-based answer generation to provide accurate and
 * contextual responses.</p>
 *
 * <p>The function works by:</p>
 * <ul>
 *   <li>Performing vector similarity search on the knowledge base</li>
 *   <li>Filtering results based on the provided filter expression</li>
 *   <li>Preparing context from retrieved documents</li>
 *   <li>Using an LLM to generate a structured response</li>
 *   <li>Returning the answer with metadata and source context</li>
 * </ul>
 *
 * <p>This implementation is designed for use in test cases and direct knowledge base
 * querying scenarios where structured responses are required.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 * @see VectorStore
 * @see LlmApiService
 * @see KnowledgeBase
 */
public class KnowledgeBaseReturnDirect
    implements Function<KnowledgeBaseReturnDirect.Request, KnowledgeBaseReturnDirect.Response> {

  private static final Logger log = LoggerFactory.getLogger(KnowledgeBaseReturnDirect.class);
  
  /** The vector store for similarity search operations. */
  private final VectorStore vectorStore;
  
  /** The LLM API service for generating responses. */
  private final LlmApiService llmApiService;
  
  /** Optional filter expression for refining search results. */
  private final String filterExpression;
  
  /** The search request configuration for vector similarity search. */
  private SearchRequest searchRequest;

  /** The prompt ID for the knowledge base tool prompt. */
  private static final String PROMPT_ID =
      "CONVERSATION_AI-KnowledgeBaseTool-Knowledge_Base_Tool_prompt_v-1";

  /**
   * Constructs a new KnowledgeBaseReturnDirect function with the specified dependencies.
   *
   * <p>This constructor initializes the function with a vector store, knowledge base configuration,
   * LLM API service, and optional filter expression. It sets up the search request with
   * similarity threshold and top-k parameters from the knowledge base configuration.</p>
   *
   * @param vectorStore the vector store for similarity search operations
   * @param knowledgeBase the knowledge base configuration containing search parameters
   * @param llmApiService the LLM API service for generating responses
   * @param filterExpression optional filter expression for refining search results
   */
  public KnowledgeBaseReturnDirect(
      VectorStore vectorStore,
      KnowledgeBase knowledgeBase,
      LlmApiService llmApiService,
      String filterExpression) {
    String metaData = knowledgeBase.getVectorMetaData();
    log.info("initializing KnowledgeBaseReturnDirect tool. metaData : {}", metaData);
    this.vectorStore = vectorStore;
    this.filterExpression = filterExpression;
    this.searchRequest =
        SearchRequest.builder()
            .similarityThreshold(knowledgeBase.getSimilarityThreshold())
            .topK(knowledgeBase.getTopK())
            .build();
    this.llmApiService = llmApiService;
    log.info(
        "KnowledgeBaseReturnDirect tool initialized with filterExpression : {}", filterExpression);
  }

  /**
   * Processes a knowledge base query and returns a structured response.
   *
   * <p>This method takes a question as input and performs the following steps:</p>
   * <ol>
   *   <li>Performs vector similarity search using the configured search request</li>
   *   <li>Applies filter expression if provided</li>
   *   <li>Prepares context from retrieved documents</li>
   *   <li>Uses LLM to generate a structured response</li>
   *   <li>Returns the response with answer, metadata, and source context</li>
   * </ol>
   *
   * @param request the request containing the question to be answered
   * @return a structured response with answer, metadata, and source context
   */
  @Override
  public Response apply(Request request) {
    log.info("inside function KnowledgeBase. request : {}", request.question());
    if (filterExpression != null) {
      searchRequest =
          SearchRequest.from(searchRequest)
              .query(request.question())
              .filterExpression(filterExpression)
              .build();
    }
    List<Document> documents = vectorStore.similaritySearch(searchRequest);
    String context = prepareContext(documents);

    BeanOutputConverter<Response> converter = new BeanOutputConverter<>(Response.class);

    String response =
        llmApiService.executePromptV1(
            PROMPT_ID,
            Map.of(
                "context",
                context,
                "userMessage",
                request.question(),
                "format",
                converter.getFormat()));
    return converter.convert(response);
  }

  /**
   * Request record for knowledge base queries.
   *
   * @param question the question to be answered by the knowledge base
   */
  public record Request(@JsonProperty("question") String question) {}

  /**
   * Response record for knowledge base query results.
   *
   * @param content the answer to the question
   * @param metadata metadata of the document used for answering
   * @param contextLine the exact line the answer is taken from
   */
  public record Response(
      @JsonPropertyDescription("answer of the question") String content,
      @JsonPropertyDescription("metadata of the document used for answering")
          Map<String, Object> metadata,
      @JsonPropertyDescription("exact line the answer is taken from") String contextLine) {}

  /**
   * Prepares context from a list of documents for LLM processing.
   *
   * <p>This method formats the retrieved documents into a structured context string
   * that includes both the document content and metadata. The context is formatted
   * in a way that's suitable for LLM processing and includes separators between
   * different documents.</p>
   *
   * @param documents the list of documents retrieved from vector search
   * @return a formatted context string containing document content and metadata
   */
  private String prepareContext(List<Document> documents) {
    StringBuilder context = new StringBuilder();
    for (Document document : documents) {
      context.append("content : ").append(document.getText()).append("\n");
      context
          .append("metadata : ")
          .append(JsonUtils.convertToJSON(document.getMetadata()))
          .append("\n-------------");
    }
    return context.toString();
  }
}
