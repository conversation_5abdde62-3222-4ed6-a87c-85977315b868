/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dao.KnowledgeBaseDao;
import com.enttribe.promptanalyzer.dao.McpServerDao;
import com.enttribe.promptanalyzer.dao.PromptDao;
import com.enttribe.promptanalyzer.dao.TagDao;
import com.enttribe.promptanalyzer.dao.TestCaseDao;
import com.enttribe.promptanalyzer.dao.ToolDao;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.KnowledgeBase;
import com.enttribe.promptanalyzer.model.McpServer;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.model.Tag;
import com.enttribe.promptanalyzer.model.TestCase;
import com.enttribe.promptanalyzer.model.Tool;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Service for migrating and assigning nano IDs to various entities in batches.
 *
 * <p>This service provides batch and full migration operations for prompts, knowledge bases, MCP
 * servers, tools, test cases, and tags, ensuring each entity receives a unique nano ID.
 * Responsibilities:
 *
 * <ul>
 *   <li>Batch and full migration of nano IDs for all supported entities
 *   <li>Transactional safety for batch operations
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class NanoIdMigrationService {

  private static final int BATCH_SIZE = 100;
  private final PromptDao promptDao;
  private final KnowledgeBaseDao knowledgeBaseDao;
  private final McpServerDao mcpServerDao;
  private final ToolDao toolDao;
  private final TestCaseDao testCaseDao;
  private final TagDao tagDao;

  /**
   * Migrates a batch of prompts that do not have nano IDs.
   *
   * @return the number of prompts updated in this batch
   */
  @Transactional
  public int migrateBatchPrompt() {
    log.debug("Inside @method migrateBatchPrompt");
    try {
      List<Prompt> batch = promptDao.findFirstBatchWithNullNanoId(PageRequest.of(0, BATCH_SIZE));
      if (batch.isEmpty()) {
        return 0;
      }
      for (Prompt prompt : batch) {
        prompt.setNanoId(NanoIdUtils.randomNanoId());
      }
      promptDao.saveAll(batch);
      log.info("Successfully migrated {} prompts in batch", batch.size());
      return batch.size();
    } catch (DataAccessException e) {
      log.error("Database error during prompt migration: {}", e.getMessage(), e);
      throw new BusinessException("Database error while migrating prompts.", e);
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error in @method migrateBatchPrompt : {}", e.getMessage(), e);
      throw new BusinessException("Invalid prompt data: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error in @method migrateBatchPrompt : {}", e.getMessage(), e);
      throw new BusinessException("Failed to migrate batch prompts.");
    }
  }

  /** Migrates all prompts in batches until all have nano IDs. */
  public void migrateAllPrompt() {
    log.debug("Inside @method migrateAllPrompt");
    try {
      int total = 0;
      int processed;
      do {
        processed = migrateBatchPrompt();
        total += processed;
        log.debug(PromptConstants.PROCESSED_PROMPT_BATCH_OF, processed);
      } while (processed == BATCH_SIZE);
      log.debug(PromptConstants.TOTAL_RECORDS_UPDATED_FOR_PROMPT, total);
      log.info("Successfully migrated all prompts. Total updated: {}", total);
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error in @method migrateAllPrompt : {}", e.getMessage(), e);
      throw new BusinessException("Invalid prompt data: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error in @method migrateAllPrompt : {}", e.getMessage(), e);
      throw new BusinessException("Failed to migrate all prompts.");
    }
  }

  /**
   * Migrates a batch of knowledge bases that do not have nano IDs.
   *
   * @return the number of knowledge bases updated in this batch
   */
  @Transactional
  public int migrateBatchKnowledgeBase() {
    log.debug("Inside @method migrateBatchKnowledgeBase");
    try {
      List<KnowledgeBase> batch =
          knowledgeBaseDao.findFirstBatchWithNullNanoId(PageRequest.of(0, BATCH_SIZE));

      if (batch.isEmpty()) {
        return 0;
      }

      for (KnowledgeBase knowledgeBase : batch) {
        knowledgeBase.setNanoId(NanoIdUtils.randomNanoId());
      }
      knowledgeBaseDao.saveAll(batch);
      log.info("Successfully migrated {} knowledge bases in batch", batch.size());
      return batch.size();
    } catch (DataAccessException e) {
      log.error("Database error during knowledge base migration: {}", e.getMessage(), e);
      throw new BusinessException("Database error while migrating knowledge bases.", e);
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error in @method migrateBatchKnowledgeBase : {}", e.getMessage(), e);
      throw new BusinessException("Invalid knowledge base data: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error in @method migrateBatchKnowledgeBase : {}", e.getMessage(), e);
      throw new BusinessException("Failed to migrate batch knowledge bases.");
    }
  }

  /** Migrates all knowledge bases in batches until all have nano IDs. */
  public void migrateAllKnowledgeBase() {
    log.debug("Inside @method migrateAllKnowledgeBase");
    try {
      int total = 0;
      int processed;
      do {
        processed = migrateBatchKnowledgeBase();
        total += processed;
        log.debug(PromptConstants.PROCESSED_PROMPT_BATCH_OF, processed);
      } while (processed == BATCH_SIZE);
      log.debug(PromptConstants.TOTAL_RECORDS_UPDATED_FOR_PROMPT, total);
      log.info("Successfully migrated all knowledge bases. Total updated: {}", total);
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error in @method migrateAllKnowledgeBase : {}", e.getMessage(), e);
      throw new BusinessException("Invalid knowledge base data: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error in @method migrateAllKnowledgeBase : {}", e.getMessage(), e);
      throw new BusinessException("Failed to migrate all knowledge bases.");
    }
  }

  /**
   * Migrates a batch of MCP servers that do not have nano IDs.
   *
   * @return the number of MCP servers updated in this batch
   */
  @Transactional
  public int migrateBatchMcpServer() {
    log.debug("Inside @method migrateBatchMcpServer");
    try {
      List<McpServer> batch =
          mcpServerDao.findFirstBatchWithNullNanoId(PageRequest.of(0, BATCH_SIZE));

      if (batch.isEmpty()) {
        return 0;
      }

      for (McpServer mcpServer : batch) {
        mcpServer.setNanoId(NanoIdUtils.randomNanoId());
      }
      mcpServerDao.saveAll(batch);
      log.info("Successfully migrated {} MCP servers in batch", batch.size());
      return batch.size();
    } catch (DataAccessException e) {
      log.error("Database error during MCP server migration: {}", e.getMessage(), e);
      throw new BusinessException("Database error while migrating MCP servers.", e);
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error in @method migrateBatchMcpServer : {}", e.getMessage(), e);
      throw new BusinessException("Invalid MCP server data: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error in @method migrateBatchMcpServer : {}", e.getMessage(), e);
      throw new BusinessException("Failed to migrate batch MCP servers.");
    }
  }

  /** Migrates all MCP servers in batches until all have nano IDs. */
  public void migrateAllMcpServer() {
    log.debug("Inside @method migrateAllMcpServer");
    try {
      int total = 0;
      int processed;
      do {
        processed = migrateBatchMcpServer();
        total += processed;
        log.debug(PromptConstants.PROCESSED_PROMPT_BATCH_OF, processed);
      } while (processed == BATCH_SIZE);
      log.debug(PromptConstants.TOTAL_RECORDS_UPDATED_FOR_PROMPT, total);
      log.info("Successfully migrated all MCP servers. Total updated: {}", total);
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error in @method migrateAllMcpServer : {}", e.getMessage(), e);
      throw new BusinessException("Invalid MCP server data: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error in @method migrateAllMcpServer : {}", e.getMessage(), e);
      throw new BusinessException("Failed to migrate all MCP servers.");
    }
  }

  /**
   * Migrates a batch of tools that do not have nano IDs.
   *
   * @return the number of tools updated in this batch
   */
  @Transactional
  public int migrateBatchTool() {
    log.debug("Inside @method migrateBatchTool");
    try {
      List<Tool> batch = toolDao.findFirstBatchWithNullNanoId(PageRequest.of(0, BATCH_SIZE));
      if (batch.isEmpty()) {
        return 0;
      }
      for (Tool tool : batch) {
        tool.setNanoId(NanoIdUtils.randomNanoId());
      }
      toolDao.saveAll(batch);
      log.info("Successfully migrated {} tools in batch", batch.size());
      return batch.size();
    } catch (DataAccessException e) {
      log.error("Database error during tool migration: {}", e.getMessage(), e);
      throw new BusinessException("Database error while migrating tools.", e);
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error in @method migrateBatchTool : {}", e.getMessage(), e);
      throw new BusinessException("Invalid tool data: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error in @method migrateBatchTool : {}", e.getMessage(), e);
      throw new BusinessException("Failed to migrate batch tools.");
    }
  }

  /** Migrates all tools in batches until all have nano IDs. */
  public void migrateAllTool() {
    log.debug("Inside @method migrateAllTool");
    try {
      int total = 0;
      int processed;
      do {
        processed = migrateBatchTool();
        total += processed;
        log.debug(PromptConstants.PROCESSED_PROMPT_BATCH_OF, processed);
      } while (processed == BATCH_SIZE);
      log.debug(PromptConstants.TOTAL_RECORDS_UPDATED_FOR_PROMPT, total);
      log.info("Successfully migrated all tools. Total updated: {}", total);
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error in @method migrateAllTool : {}", e.getMessage(), e);
      throw new BusinessException("Invalid tool data: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error in @method migrateAllTool : {}", e.getMessage(), e);
      throw new BusinessException("Failed to migrate all tools.");
    }
  }

  /**
   * Migrates a batch of test cases that do not have nano IDs.
   *
   * @return the number of test cases updated in this batch
   */
  @Transactional
  public int migrateBatchTestCase() {
    log.debug("Inside @method migrateBatchTestCase");
    try {
      List<TestCase> batch =
          testCaseDao.findFirstBatchWithNullNanoId(PageRequest.of(0, BATCH_SIZE));

      if (batch.isEmpty()) {
        return 0;
      }

      for (TestCase tool : batch) {
        tool.setNanoId(NanoIdUtils.randomNanoId());
      }
      testCaseDao.saveAll(batch);
      log.info("Successfully migrated {} test cases in batch", batch.size());
      return batch.size();
    } catch (DataAccessException e) {
      log.error("Database error during test case migration: {}", e.getMessage(), e);
      throw new BusinessException("Database error while migrating test cases.", e);
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error in @method migrateBatchTestCase : {}", e.getMessage(), e);
      throw new BusinessException("Invalid test case data: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error in @method migrateBatchTestCase : {}", e.getMessage(), e);
      throw new BusinessException("Failed to migrate batch test cases.");
    }
  }

  /** Migrates all test cases in batches until all have nano IDs. */
  public void migrateAllTestCase() {
    log.debug("Inside @method migrateAllTestCase");
    try {
      int total = 0;
      int processed;
      do {
        processed = migrateBatchTestCase();
        total += processed;
        log.debug("Processed tag batch of: {} ", processed);
      } while (processed == BATCH_SIZE);
      log.debug("Total records updated for tag : {}", total);
      log.info("Successfully migrated all test cases. Total updated: {}", total);
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error in @method migrateAllTestCase : {}", e.getMessage(), e);
      throw new BusinessException("Invalid test case data: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error in @method migrateAllTestCase : {}", e.getMessage(), e);
      throw new BusinessException("Failed to migrate all test cases.");
    }
  }

  /**
   * Migrates a batch of tags that do not have nano IDs.
   *
   * @return the number of tags updated in this batch
   */
  @Transactional
  public int migrateBatchTag() {
    log.debug("Inside @method migrateBatchTag");
    try {
      List<Tag> batch = tagDao.findFirstBatchWithNullNanoId(PageRequest.of(0, BATCH_SIZE));
      if (batch.isEmpty()) {
        return 0;
      }
      for (Tag tool : batch) {
        tool.setNanoId(NanoIdUtils.randomNanoId());
      }
      tagDao.saveAll(batch);
      log.info("Successfully migrated {} tags in batch", batch.size());
      return batch.size();
    } catch (DataAccessException e) {
      log.error("Database error during tag migration: {}", e.getMessage(), e);
      throw new BusinessException("Database error while migrating tags.", e);
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error in @method migrateBatchTag : {}", e.getMessage(), e);
      throw new BusinessException("Invalid tag data: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error in @method migrateBatchTag : {}", e.getMessage(), e);
      throw new BusinessException("Failed to migrate batch tags.");
    }
  }

  /** Migrates all tags in batches until all have nano IDs. */
  public void migrateAllTag() {
    log.debug("Inside @method migrateAllTag");
    try {
      int total = 0;
      int processed;
      do {
        processed = migrateBatchTag();
        total += processed;
        log.debug("Processed Tag batch of: {}", processed);
      } while (processed == BATCH_SIZE);
      log.debug("Total records updated for Tag :  {}", total);
      log.info("Successfully migrated all tags. Total updated: {}", total);
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error in @method migrateAllTag : {}", e.getMessage(), e);
      throw new BusinessException("Invalid tag data: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error in @method migrateAllTag : {}", e.getMessage(), e);
      throw new BusinessException("Failed to migrate all tags.");
    }
  }
}
