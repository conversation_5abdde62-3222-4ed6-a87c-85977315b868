/*
 * Copyright (c) 2024 Vwaves Technologies Private Limited
 *
 * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.
 * Product: Aspose.Total for Java
 * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations
 * Order ID: 250610115636
 *
 * This source code is part of a proprietary software system and may not be
 * distributed, modified, or reused without explicit written permission from
 * Vwaves Technologies Private Limited.
 *
 * Contact: <EMAIL>
 */
package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.ToolAudit;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * Data Access Object interface for ToolAudit entity operations. Provides methods to interact with
 * the ToolAudit table in the database for managing tool audit records.
 *
 * <p>This interface extends JpaRepository to provide standard CRUD operations and includes
 * custom query methods for retrieving tool audit records by audit ID.</p>
 *
 * @see ToolAudit
 * @see JpaRepository
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public interface ToolAuditDao extends JpaRepository<ToolAudit, Integer> {

  /**
   * Retrieves all tool audit records associated with a specific audit ID.
   *
   * @param auditId the audit ID to search for tool audit records
   * @return a list of tool audit records matching the audit ID
   */
  @Query("SELECT t FROM ToolAudit t WHERE t.auditId = :auditId")
  List<ToolAudit> getToolAuditListByAuditId(String auditId);
}
