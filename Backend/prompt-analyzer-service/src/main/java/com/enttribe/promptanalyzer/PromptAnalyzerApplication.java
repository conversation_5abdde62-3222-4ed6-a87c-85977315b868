/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer;

import com.aspose.pdf.License;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.cassandra.CassandraAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.core.io.ClassPathResource;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication(
    scanBasePackages = {
      "com.enttribe.commons",
      "com.enttribe.promptanalyzer",
      "com.enttribe.product.security.spring.userdetails",
      "com.enttribe.platform.resourceinfo",
      "com.enttribe.authorization",
      "com.enttribe.platform.utility",
      "com.enttribe.usermanagement"
    },
    exclude = {
      CassandraAutoConfiguration.class,
      SecurityAutoConfiguration.class,
      ManagementWebSecurityAutoConfiguration.class,
      RedisAutoConfiguration.class
    })
@ComponentScan(
    basePackages = "com.enttribe",
    excludeFilters =
        @ComponentScan.Filter(
            type = FilterType.ASSIGNABLE_TYPE,
            classes = {
              com.enttribe.product.storagesystem.rest.impl.StorageRestImpl.class,
              com.enttribe.core.generic.utils.CacheConfig.class
            }))
@EntityScan(
    basePackages = {
      "com.enttribe.commons",
      "com.enttribe.promptanalyzer",
      "com.enttribe.platform.resourceinfo",
      "com.enttribe.authorization",
      "com.enttribe.platform.utility",
      "com.enttribe.usermanagement"
    })
@EnableFeignClients(
    basePackages = {"com.enttribe.core.generic", "com.enttribe.core.businessconfiguration"})
@EnableAsync
@EnableRetry
public class PromptAnalyzerApplication {

  private static final Logger LOGGER = LoggerFactory.getLogger(PromptAnalyzerApplication.class);


  public static void main(String[] args) {
    SpringApplication.run(PromptAnalyzerApplication.class, args);
    loadAsposeLicense();
  }

  private static void loadAsposeLicense() {
    try {
      License license = new License();
      LOGGER.info("Aspose license loading in progress");

      ClassPathResource licenseResource = new ClassPathResource("Aspose.TotalforJava.lic");
      license.setLicense(licenseResource.getInputStream());

      LOGGER.info("Aspose license loaded successfully");
    } catch (Exception e) {
      LOGGER.error("Failed to load Aspose license: {}", e.getMessage(), e);
    }
  }
}
