/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.product.security.spring.userdetails.CustomerInfo;
import com.enttribe.promptanalyzer.config.IntentHintConfig;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.service.HintService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class HintServiceImpl implements HintService {

  public static final String METADATA = "metadata";
  public static final int STATUS_OK = 200;
  private final IntentHintConfig hintConfig;

  private static final String EMPLOYEE = "Employee";
  private static final String SCORE = "score";
  private static final String LUCENE_URL =
      "http://lucene-service.ansible.svc.cluster.local/api/search/searchLucene";
  private CustomerInfo customerInfo;

  public HintServiceImpl(IntentHintConfig hintConfig, CustomerInfo customerInfo) {
    this.hintConfig = hintConfig;
    this.customerInfo = customerInfo;
  }

  private Map<String, Object> buildResultItem(String queryText, List<Document> documents) {
    Map<String, Object> resultItem = new java.util.HashMap<>();
    resultItem.put("name", queryText);
    if (!documents.isEmpty()) {
      List<Map<String, Object>> metadata = new ArrayList<>();
      for (Document doc : documents) {
        metadata.add(doc.getMetadata());
      }
      resultItem.put(hintConfig.getMetadataKey(), metadata);
      log.debug("Successfully found {} documents for query: {}", documents.size(), queryText);
    } else {
      resultItem.put(hintConfig.getMetadataKey(), List.of());
      log.debug("No matching documents found for query: {}", queryText);
    }
    return resultItem;
  }

  @Override
  public List<Map<String, Object>> searchPlanBatch(
      List<String> queries, String type, String entityType) {
    log.debug(
        "Inside @method searchPlanBatch (with type and entityType): {} and {}", type, entityType);
    List<Map<String, Object>> results = new ArrayList<>();

    try {
      for (String queryText : queries) {
        log.debug(PromptConstants.PROCESSING_QUERY_LOG, queryText);
        List<Document> documents = getDocumentsByTypeAndEntity(queryText, type, entityType);
        Map<String, Object> resultItem =
            buildResultItem(queryText, filterUniqueDocuments(documents));
        results.add(resultItem);
      }
      log.info("Successfully processed batch search with {} queries", queries.size());
      return results;
    } catch (Exception e) {
      log.error(
          "Error in @method searchPlanBatch (with type and entityType) : {}", e.getMessage(), e);
      throw new BusinessException("Error processing batch search.", e);
    }
  }

  private List<Document> getDocumentsByTypeAndEntity(
      String queryText, String type, String entityType) {
    if (checkIfEmailOrNot(queryText)) {
      return new ArrayList<>();
    }
    if ("Document".equalsIgnoreCase(entityType) || "Documents".equalsIgnoreCase(entityType)) {
      return new ArrayList<>();
    }
    
    if ("who".equalsIgnoreCase(type)) {
      return handleWhoTypeQuery(queryText, entityType);
    } else if ("what".equalsIgnoreCase(type)) {
      return handleWhatTypeQuery(queryText, entityType);
    }
    
    return new ArrayList<>();
  }

  private List<Document> handleWhoTypeQuery(String queryText, String entityType) {
    if (isEmployeeOrPersonEntity(entityType)) {
      return sendGetForLuceneSearch(queryText, EMPLOYEE);
    }
    return new ArrayList<>();
  }

  private List<Document> handleWhatTypeQuery(String queryText, String entityType) {
    if (isEmployeeOrPersonEntity(entityType)) {
      return sendGetForLuceneSearch(queryText, EMPLOYEE);
    }
    
    if (isSupportedEntityType(entityType)) {
      return sendGetForLuceneSearch(queryText, entityType);
    }
    
    return new ArrayList<>();
  }

  private boolean isEmployeeOrPersonEntity(String entityType) {
    return EMPLOYEE.equalsIgnoreCase(entityType) || "Person".equalsIgnoreCase(entityType);
  }

  private boolean isSupportedEntityType(String entityType) {
    return "WorkGroup".equalsIgnoreCase(entityType) ||
           "Department".equalsIgnoreCase(entityType) ||
           "Task".equalsIgnoreCase(entityType) ||
           "Country".equalsIgnoreCase(entityType) ||
           "Location".equalsIgnoreCase(entityType) ||
           "category".equalsIgnoreCase(entityType) ||
           "dataroom".equalsIgnoreCase(entityType);
  }

  private List<Document> filterUniqueDocuments(List<Document> documents) {
    log.debug(
        "Inside @method filterUniqueDocuments with @documents size: {}",
        documents == null ? 0 : documents.size());
    try {
      if (documents == null || documents.isEmpty()) {
        return Collections.emptyList();
      }
      // Map to store unique documents by ID
      Map<String, Document> uniqueDocMap = new HashMap<>();
      for (Document doc : documents) {
        String docId = extractDocId(doc);
        if (docId == null) {
          continue;
        }
        handleUniqueDocument(uniqueDocMap, doc, docId);
      }

      // Convert back to list and sort by score (highest first)
      List<Document> uniqueDocuments = new ArrayList<>(uniqueDocMap.values());
      uniqueDocuments.sort(
          (a, b) ->
              Double.compare(
                  Double.parseDouble(b.getMetadata().get(SCORE).toString()),
                  Double.valueOf(a.getMetadata().get(SCORE).toString())));

      log.info(
          "Filtered {} unique documents from {} total documents",
          uniqueDocuments.size(),
          documents.size());
      return uniqueDocuments;
    } catch (Exception e) {
      log.error("Error in @method filterUniqueDocuments : {}", e.getMessage(), e);
      throw new BusinessException("Error filtering unique documents.", e);
    }
  }

  private String extractDocId(Document doc) {
    Object idObj = doc.getMetadata().get("id");
    String docId = idObj != null ? idObj.toString() : null;
    if (docId == null || docId.isEmpty()) {
      log.warn("Document found without ID, skipping: {}", doc.getText());
      return null;
    }
    return docId;
  }

  private void handleUniqueDocument(
      Map<String, Document> uniqueDocMap, Document doc, String docId) {
    if (!uniqueDocMap.containsKey(docId)
        || Double.parseDouble(doc.getMetadata().get(SCORE).toString())
            > Double.parseDouble(uniqueDocMap.get(docId).getMetadata().get(SCORE).toString())) {
      if (uniqueDocMap.containsKey(docId)) {
        log.debug(
            "Replacing document with ID '{}' - new score: {}, old score: {}",
            docId,
            Double.valueOf(doc.getMetadata().get(SCORE).toString()),
            Double.valueOf(uniqueDocMap.get(docId).getMetadata().get(SCORE).toString()));
      }
      uniqueDocMap.put(docId, doc);
    } else {
      log.debug(
          "Skipping duplicate document with ID '{}' - score: {}, existing score: {}",
          docId,
          Double.valueOf(doc.getMetadata().get(SCORE).toString()),
          Double.valueOf(uniqueDocMap.get(docId).getMetadata().get(SCORE).toString()));
    }
  }

  private boolean checkIfEmailOrNot(String queryText) {
    if (queryText == null || queryText.trim().isEmpty()) {
      return false;
    }

    String emailRegex = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+$";
    return queryText.matches(emailRegex);
  }

  private List<Document> sendGetForLuceneSearch(String query, String type) {
    List<Document> documents = new ArrayList<>();

    try {
      String fullUrl = buildLuceneUrl(query, type);
      log.info("Calling Lucene API with URL: {}", fullUrl);

      String jsonResponse = executeGetRequest(fullUrl);
      if (jsonResponse == null
          || jsonResponse.trim().isEmpty()
          || "null".equals(jsonResponse.trim())) {
        log.info("Lucene response is empty or null. Skipping parsing.");
        return Collections.emptyList();
      }

      documents = parseLuceneResponse(jsonResponse);

    } catch (Exception e) {
      log.error(
          "Error occurred while calling or parsing Lucene API response: {}", e.getMessage(), e);
    }

    log.info("Total documents parsed from Lucene: {}", documents.size());
    return documents;
  }

  private String buildLuceneUrl(String query, String type) {
    String queryParam = URLEncoder.encode(query, StandardCharsets.UTF_8);
    String typeParam = URLEncoder.encode(type, StandardCharsets.UTF_8);
    Integer customerId = customerInfo.getCustomerWrapper().getId();
    log.info("Inside @method buildLuceneUrl, customerId is: {}", customerId);
    return String.format("%s?query=%s&type=%s&customerID=%d", LUCENE_URL, queryParam, typeParam, customerId);
  }

  private String executeGetRequest(String urlString) throws IOException {
    URI uri = URI.create(urlString);
    URL url = uri.toURL();
    HttpURLConnection con = (HttpURLConnection) url.openConnection();
    con.setRequestMethod("GET");
    con.setRequestProperty("Accept", "application/json");

    int status = con.getResponseCode();
    if (status != STATUS_OK) {
      log.error("Lucene API responded with non-200 status: {}", status);
      return null;
    }

    try (BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()))) {
      return in.lines().collect(Collectors.joining());
    } finally {
      con.disconnect();
    }
  }

  private List<Document> parseLuceneResponse(String jsonResponse) throws IOException {
    List<Document> documents = new ArrayList<>();
    ObjectMapper mapper = new ObjectMapper();
    JsonNode rootNode = mapper.readTree(jsonResponse);

    if (!rootNode.isArray()) {
      log.error("Expected JSON array from Lucene API but received: {}", rootNode.getNodeType());
      return documents;
    }

    for (JsonNode node : rootNode) {
      Document doc = parseSingleDocument(mapper, node);
      if (doc != null) {
        documents.add(doc);
      }
    }
    return documents;
  }

  private Document parseSingleDocument(ObjectMapper mapper, JsonNode node) {
    String text = node.hasNonNull("text") ? node.get("text").asText() : null;
    JsonNode mediaNode = node.get("media");
    boolean hasMedia = mediaNode != null && !mediaNode.isNull();

    if ((text == null && !hasMedia) || (text != null && hasMedia)) {
      log.info("Skipping invalid document (text/media constraint violated): {}", node);
      return null;
    }

    Map<String, Object> metadata = new HashMap<>();
    if (node.has(METADATA) && node.get(METADATA).isObject()) {
      node.get(METADATA)
          .fields()
          .forEachRemaining(
              entry ->
                  metadata.put(
                      entry.getKey(), mapper.convertValue(entry.getValue(), Object.class)));
    }

    log.debug("Parsed Document with text='{}' and metadata={}", text, metadata);
    return new Document(text, metadata);
  }
}
