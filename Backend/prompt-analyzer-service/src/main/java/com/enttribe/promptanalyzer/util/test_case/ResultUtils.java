/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util.test_case;

import com.enttribe.promptanalyzer.dto.result.AssertionResultResponseDto;
import com.enttribe.promptanalyzer.dto.result.TestCaseResultResponseDto;
import com.enttribe.promptanalyzer.dto.result.TestResultResponseDto;
import com.enttribe.promptanalyzer.model.AssertionResult;
import com.enttribe.promptanalyzer.model.TestCaseResult;
import com.enttribe.promptanalyzer.model.TestResult;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

/**
 * Utility class for converting test result entities to response DTOs.
 *
 * <p>This class provides static methods for converting various test result entities
 * (AssertionResult, TestCaseResult, TestResult) to their corresponding response DTOs.
 * It handles null checks and provides safe conversion with proper mapping of all
 * relevant fields.</p>
 *
 * <p>The utility methods ensure consistent data transformation between the domain
 * layer and the API response layer, maintaining data integrity and providing
 * clean separation of concerns.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 * @see AssertionResult
 * @see TestCaseResult
 * @see TestResult
 * @see AssertionResultResponseDto
 * @see TestCaseResultResponseDto
 * @see TestResultResponseDto
 */
@Slf4j
public class ResultUtils {

  /**
   * Private constructor to prevent instantiation of this utility class.
   *
   * @throws IllegalStateException if an attempt is made to instantiate this class
   */
  private ResultUtils() {
    throw new IllegalStateException("utility class can not be instantiated");
  }

  /**
   * Converts a set of AssertionResult entities to a list of AssertionResultResponseDto objects.
   *
   * <p>This method processes a collection of assertion result entities and converts
   * each one to its corresponding response DTO. It uses the
   * {@link #toAssertionResultResponseDto(AssertionResult)} method for individual conversions.</p>
   *
   * @param assertionResults the set of assertion result entities to convert
   * @return a list of AssertionResultResponseDto objects, or empty list if input is null
   */
  public static List<AssertionResultResponseDto> toAssertionResultResponseDtos(
      Set<AssertionResult> assertionResults) {
    return assertionResults.stream().map(ResultUtils::toAssertionResultResponseDto).toList();
  }

  /**
   * Converts an AssertionResult entity to an AssertionResultResponseDto.
   *
   * <p>This method maps all relevant fields from the AssertionResult entity to the
   * corresponding response DTO. It handles null input gracefully by returning null.
   * The method maps fields including ID, actual/expected values, parameter names,
   * pass/fail status, and metadata.</p>
   *
   * @param entity the AssertionResult entity to convert
   * @return the corresponding AssertionResultResponseDto, or null if input is null
   */
  public static AssertionResultResponseDto toAssertionResultResponseDto(AssertionResult entity) {
    if (entity == null) {
      return null;
    }

    String expected = entity.getExpected();
    String jsonPointer = entity.getParameterName();

    return AssertionResultResponseDto.builder()
        .id(entity.getNanoId())
        .actual(entity.getActual())
        .expected(expected)
        .parameterName(jsonPointer)
        .isPassed(entity.isPassed())
        .userQuestion(entity.getUserQuestion())
        .assertionType(entity.getAssertionType())
        .matchStrategy(entity.getMatchStrategy())
        .comment(entity.getComment())
        .accuracyScore(entity.getAccuracyScore())
        .createdTime(entity.getCreatedTime())
        .build();
  }

  /**
   * Converts a TestCaseResult entity to a TestCaseResultResponseDto.
   *
   * <p>This method maps all relevant fields from the TestCaseResult entity to the
   * corresponding response DTO. It handles null input gracefully and also processes
   * nested entities like AgentTestCase and AssertionResults. The method includes
   * performance metrics, token counts, and assertion results.</p>
   *
   * @param entity the TestCaseResult entity to convert
   * @return the corresponding TestCaseResultResponseDto, or null if input is null
   */
  public static TestCaseResultResponseDto toTestCaseResultResponseDto(TestCaseResult entity) {
    if (entity == null) {
      return null;
    }

    return TestCaseResultResponseDto.builder()
        .id(entity.getNanoId())
        .accuracy(entity.getAccuracy())
        .latency(entity.getLatency())
        .isError(entity.isError())
        .runNumber(entity.getRunNumber())
        .promptToken(entity.getPromptToken())
        .generationTokens(entity.getGenerationTokens())
        .failureReason(entity.getFailureReason())
        .userQuestion(
            entity.getAgentTestCase() != null ? entity.getAgentTestCase().getUserQuestion() : null)
        .advisors(
            entity.getAgentTestCase() != null ? entity.getAgentTestCase().getAdvisors() : null)
        .name(entity.getAgentTestCase() != null ? entity.getAgentTestCase().getName() : null)
        .description(
            entity.getAgentTestCase() != null ? entity.getAgentTestCase().getDescription() : null)
        .isPassed(entity.isPassed())
        .assertionsPassed(entity.getAssertionsPassed())
        .assertionsFailed(entity.getAssertionsFailed())
        .createdTime(entity.getCreatedTime())
        .assertionResultsCount(
            entity.getAssertionResults() != null ? entity.getAssertionResults().size() : 0)
        .assertionResults(
            entity.getAssertionResults() != null && !entity.getAssertionResults().isEmpty()
                ? toAssertionResultResponseDtos(entity.getAssertionResults())
                : new ArrayList<>())
        .build();
  }

  /**
   * Converts a TestResult entity to a TestResultResponseDto.
   *
   * <p>This method maps all relevant fields from the TestResult entity to the
   * corresponding response DTO. It handles null input gracefully and processes
   * nested entities like TestSuite. The method includes comprehensive test metrics,
   * execution details, and summary statistics.</p>
   *
   * @param entity the TestResult entity to convert
   * @return the corresponding TestResultResponseDto, or null if input is null
   */
  public static TestResultResponseDto toTestResultResponseDto(TestResult entity) {
    if (entity == null) {
      return null;
    }

    return TestResultResponseDto.builder()
        .id(entity.getNanoId())
        .testSuiteName(entity.getTestSuite() != null ? entity.getTestSuite().getName() : null)
        .agentName(
            (entity.getTestSuite() != null && entity.getTestSuite().getAgentName() != null)
                ? entity.getTestSuite().getAgentName()
                : null)
        .agentType(
            (entity.getTestSuite() != null && entity.getTestSuite().getAgentType() != null)
                ? entity.getTestSuite().getAgentType()
                : null)
        .executionId(entity.getExecutionId())
        .accuracy(entity.getAccuracy())
        .latency(entity.getLatency())
        .consistency(entity.getConsistency())
        .numberOfRuns(entity.getNumberOfRuns())
        .testCasesPassed(entity.getTestCasesPassed())
        .testCasesFailed(entity.getTestCasesFailed())
        .promptToken(entity.getPromptToken())
        .generationTokens(entity.getGenerationTokens())
        .model(entity.getModel())
        .description(entity.getDescription())
        .status(entity.getStatus())
        .deleted(entity.getDeleted())
        .createdTime(entity.getCreatedTime())
        .modifiedTime(entity.getModifiedTime())
        .testCaseResultsCount(
            entity.getTestCaseResults() != null ? entity.getTestCaseResults().size() : 0)
        .build();
  }
}
