/*
 * Copyright (c) 2024 Vwaves Technologies Private Limited
 *
 * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.
 * Product: Aspose.Total for Java
 * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations
 * Order ID: 250610115636
 *
 * This source code is part of a proprietary software system and may not be
 * distributed, modified, or reused without explicit written permission from
 * Vwaves Technologies Private Limited.
 *
 * Contact: <EMAIL>
 */
package com.enttribe.promptanalyzer.service;

import java.util.List;
import java.util.Map;

/**
 * Service interface for managing test execution operations in version 1 of the test execution system.
 * 
 * <p>This interface provides methods for executing test suites, retrieving input schemas,
 * and re-executing tests with different configurations. It supports the core functionality
 * needed for automated test execution and result management.</p>
 *
 * <p>Key responsibilities:</p>
 * <ul>
 *   <li>Retrieve input schemas for test configurations</li>
 *   <li>Execute test suites with specified parameters</li>
 *   <li>Re-execute tests with updated configurations</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface TestExecutionServiceV1 {

  /**
   * Retrieves the input schema for a given test configuration.
   * 
   * <p>This method returns the schema definition that describes the expected input format
   * for a specific test type. The schema helps validate and structure test inputs.</p>
   *
   * @param name the name of the test configuration
   * @param type the type of test for which to retrieve the schema
   * @return a list of strings representing the input schema definition
   */
  List<String> getInputSchema(String name, String type);

  /**
   * Executes a test suite with the specified parameters.
   * 
   * <p>This method initiates the execution of a complete test suite, linking it to a specific
   * test result record and using the specified provider for execution.</p>
   *
   * @param testSuiteId the unique identifier of the test suite to execute
   * @param testResultId the unique identifier of the test result record to associate with this execution
   * @param provider the provider/service to use for test execution
   */
  void executeTestSuite(String testSuiteId, long testResultId, String provider);

  /**
   * Re-executes a test with updated configuration parameters.
   * 
   * <p>This method allows for re-running tests with modified parameters or configurations.
   * The request map contains the updated configuration and test parameters.</p>
   *
   * @param request a map containing the updated test configuration and parameters
   */
  void reExecuteTest(Map<String, Object> request);
}
