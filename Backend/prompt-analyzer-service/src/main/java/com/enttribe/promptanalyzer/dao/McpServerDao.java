/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.McpServer;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Data Access Object interface for McpServer entity operations. Provides methods to interact with
 * the MCP_SERVER table in the database.
 *
 * <AUTHOR>
 * @version 1.0
 * @see McpServer
 * @see JpaRepository
 */
@Repository
public interface McpServerDao extends JpaRepository<McpServer, Integer> {

  /**
   * Finds all MCP servers by their nanoIds that are not deleted.
   *
   * @param nanoIds the list of nanoIds to search for
   * @return a list of MCP servers matching the provided nanoIds that are not deleted
   */
  @Query("SELECT m FROM McpServer m WHERE m.nanoId IN :nanoIds AND m.deleted = false")
  List<McpServer> findByNanoIds(@Param("nanoIds") List<String> nanoIds);

  /**
   * Finds an MCP server by its nanoId.
   *
   * @param nanoId the nanoId to search for
   * @return an Optional containing the MCP server if found, empty otherwise
   */
  @Query("SELECT m FROM McpServer m WHERE m.nanoId = :nanoId")
  Optional<McpServer> findByNanoId(@Param("nanoId") String nanoId);

  /**
   * Finds the first batch of MCP servers with null nanoId for pagination.
   *
   * @param pageable the pagination parameters
   * @return a list of MCP servers with null nanoId
   */
  @Query("SELECT m FROM McpServer m WHERE m.nanoId IS NULL")
  List<McpServer> findFirstBatchWithNullNanoId(Pageable pageable);
}
