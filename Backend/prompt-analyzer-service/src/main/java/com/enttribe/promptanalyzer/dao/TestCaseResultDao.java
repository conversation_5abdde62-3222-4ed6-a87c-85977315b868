/*
 * Copyright (c) 2024 Vwaves Technologies Private Limited
 *
 * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.
 * Product: Aspose.Total for Java
 * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations
 * Order ID: 250610115636
 *
 * This source code is part of a proprietary software system and may not be
 * distributed, modified, or reused without explicit written permission from
 * Vwaves Technologies Private Limited.
 *
 * Contact: <EMAIL>
 */
package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.TestCaseResult;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * Data Access Object interface for TestCaseResult entity operations. Provides methods to interact
 * with the TestCaseResult table in the database for managing test case execution results.
 *
 * <p>This interface extends JpaRepository to provide standard CRUD operations and includes
 * custom query methods for retrieving test case results by nanoId.</p>
 *
 * @see TestCaseResult
 * @see JpaRepository
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public interface TestCaseResultDao extends JpaRepository<TestCaseResult, Long> {

  /**
   * Finds a test case result by its nanoId.
   *
   * @param nanoId the nanoId to search for
   * @return an Optional containing the test case result if found, empty otherwise
   */
  Optional<TestCaseResult> findByNanoId(String nanoId);
}
