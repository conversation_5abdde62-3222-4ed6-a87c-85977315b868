/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.aspose.cells.Cells;
import com.aspose.cells.DateTime;
import com.aspose.cells.Workbook;
import com.aspose.cells.Worksheet;
import com.aspose.cells.WorksheetCollection;
import com.aspose.pdf.TextAbsorber;
import com.aspose.words.Cell;
import com.aspose.words.Node;
import com.aspose.words.NodeType;
import com.aspose.words.Paragraph;
import com.aspose.words.Row;
import com.aspose.words.Table;
import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.enttribe.product.security.spring.userdetails.CustomerInfo;
import com.enttribe.promptanalyzer.advisors.QuestionAnswerAdvisor;
import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dao.KnowledgeBaseDao;
import com.enttribe.promptanalyzer.dto.knowledge_base.DocumentRequestDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseResponseDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseSdkDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.VectorResponseDto;
import com.enttribe.promptanalyzer.exception.AccessDeniedException;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.DuplicateResourceException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.manager.InferenceManager;
import com.enttribe.promptanalyzer.model.KnowledgeBase;
import com.enttribe.promptanalyzer.service.ApiService;
import com.enttribe.promptanalyzer.service.KnowledgeBaseService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.knuddels.jtokkit.api.EncodingType;
import com.opencsv.CSVReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVRecord;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.document.Document;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.tokenizer.JTokkitTokenCountEstimator;
import org.springframework.ai.tokenizer.TokenCountEstimator;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.filter.Filter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * Implementation of the {@link KnowledgeBaseService} interface. This class provides the actual
 * business logic for managing KnowledgeBase. for a specific application. It interacts with the data
 * access layer to fetch and modify KnowledgeBase data.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KnowledgeBaseServiceImpl implements KnowledgeBaseService {

  public static final String CUSTOM_AGENT_FILTER = "f0b90d7dc3864b18a1c59cf99b497324abc";
  public static final String CUSTOM_AGENT_FILTER_KEY = "custom_agent_id";
  private static final String NANO_ID = "nanoid";
  private final KnowledgeBaseDao knowledgeDao;
  private final CustomFilter customFilter;
  private final VectorStore vectorStore;
  private final ApiService apiService;
  private final InferenceManager inferenceManager;
  private final S3Service s3Service;
  private final CustomerInfo customerInfo;

  private static final String KNOWLEDGE_BASE_FILTER_KEY = "doc_id";

  private static final int DEFAULT_CHUNK_SIZE = 2000;
  private static final int MAX_TOKENS_PER_CHUNK = 510;
  private static final int MID_DIVISOR = 2;
  private static final int LARGE_CHUNK_SIZE = 4000;
  private static final int BATCH_SIZE = 10;
  private static final int MAX_IMPORT_RECORDS = 100;

  @Value("${spring.ai.vectorstore.redis.index-name}")
  private String indexName;

  @Value("${spring.ai.vectorstore.redis.prefix}")
  private String redisPrefix;

  @Value("${vector.store.type}")
  private String vectorDatabase;

  private static final List<String> csvColumnHeader =
      List.of(
          "Name",
          "Description",
          "VectorMetaData",
          "TopK",
          "SimilarityThreshold",
          "Type",
          "webSiteUrl",
          "fileName",
          "websiteTaskStatus",
          "websiteTaskId",
          "websiteTaskError",
          PromptConstants.COLLECTION_NAME,
          "docType",
          "docId",
          "filter",
          "docMetaData",
          "isContext",
          "tables",
          "integration",
          "returnDirect");

  @Override
  public KnowledgeBaseResponseDto getKnowledgeBaseById(Integer id) {
    log.debug(
        "Inside @class KnowledgeBaseServiceImpl @method getKnowledgeBaseById with ID: {}", id);
    KnowledgeBase knowledgeBase =
        knowledgeDao
            .findById(id)
            .orElseThrow(
                () -> {
                  log.warn(
                      "Inside @method getKnowledgeBaseById , KnowledgeBase not found for ID: {}",
                      id);
                  return new ResourceNotFoundException("Knowledge not found for ID");
                });
    return KnowledgeConverter.convertKnowledgeDto(knowledgeBase);
  }

  @Override
  public KnowledgeBaseResponseDto getKnowledgeBaseByIdV1(String id) {
    log.debug(
        "Inside @class KnowledgeBaseServiceImpl @method getKnowledgeBaseByIdV1 with nanoId: {}",
        id);
    KnowledgeBase knowledgeBase =
        knowledgeDao
            .findByNanoId(id)
            .orElseThrow(
                () -> {
                  log.warn(
                      "Inside @method getKnowledgeBaseByIdV1 , KnowledgeBase not found for nanoId: {}",
                      id);
                  return new ResourceNotFoundException("Knowledge not found for nanoId");
                });
    return KnowledgeConverter.convertKnowledgeDto(knowledgeBase);
  }

  @Override
  public Map<String, String> saveDocument(DocumentRequestDto dto) {
    log.debug("Inside @class KnowledgeBaseServiceImpl @method saveDocument");
    KnowledgeBase knowledgeBase = new KnowledgeBase();
    Map<String, String> result = new HashMap<>();
    List<List<Document>> documents = new ArrayList<>();
    List<String> s3StorageFileNames = new ArrayList<>();
    try {
      mapToEntity(dto, knowledgeBase);
      switch (dto.getType()) {
        case "DOCUMENT" -> {
          List<MultipartFile> files = dto.getFile();
          Set<String> fileNames = extractFileNames(files);

          for (MultipartFile file : files) {
            fileNames.add(file.getOriginalFilename());
            List<Document> content = extractContentFromFile(file);
            if (content != null && !content.isEmpty()) {
              documents.add(content);
            }
          }
          // Join file names with a comma
          knowledgeBase.setFileName(String.join(", ", fileNames));
          log.debug("All files: {}", fileNames);

          VectorResponseDto vectorResponse = saveInVectorFromDocReader(documents);
          log.info("Going to save file on s3service");
          s3StorageFileNames = s3Service.uploadFileToS3(files, vectorResponse.getFilter(), "");
          knowledgeBase.setS3FileNames(JsonUtils.convertToJSON(s3StorageFileNames));
          knowledgeBase.setFilter(vectorResponse.getFilter());
          knowledgeBase.setDocMetaData(JsonUtils.convertToJSON(vectorResponse.getMetadata()));
          knowledgeBase.setDocId(JsonUtils.convertToJSON(vectorResponse.getDocIds()));
        }
        case "SQL" -> {
          List<String> tableNames = dto.getTables();
          List<String> docIds = new ArrayList<>();
          for (String tableName : tableNames) {
            Map<String, Object> docMetaData = Map.of(KNOWLEDGE_BASE_FILTER_KEY, tableName);
            String tableSchema = apiService.getTableSchema(dto.getIntegration(), tableName);
            Document document = new Document(tableSchema);
            document.getMetadata().putAll(docMetaData);
            docIds.add(document.getId());
            vectorStore.accept(List.of(document));
            log.debug("Document is successfully save in vector. table : {}", tableName);
          }
          knowledgeBase.setDocId(JsonUtils.convertToJSON(docIds));
          knowledgeBase.setDescription(
              String.format(
                  """
                                            Tool is designed to assist an LLM in constructing valid SQL queries by providing the schema details of a specified database table.
                                            Given a table name as input, this tool retrieves and returns its schema, including column names, data types, constraints, and other relevant metadata.
                                            It contains schema for following tables:
                                            %s
                                            """,
                  tableNames));
          knowledgeBase.setTables(JsonUtils.convertToJSON(tableNames));
          knowledgeBase.setIntegration(dto.getIntegration());
        }
        case "WEBSITE" -> processWebsiteData(dto, knowledgeBase);
        case "COLLECTION" -> {
          Map<String, String> vectorMetaData =
              Map.of(
                  PromptConstants.VECTOR_DATABASE,
                  vectorDatabase,
                  PromptConstants.INDEX_NAME,
                  indexName,
                  PromptConstants.COLLECTION_NAME,
                  dto.getCollectionName(),
                  PromptConstants.EMBEDDING_MODEL_KEY,
                  PromptConstants.EMBEDDING_MODEL,
                  PromptConstants.CHAT_MODEL_KEY,
                  PromptConstants.CHAT_MODEL_VALUE,
                  PromptConstants.PROVIDER,
                  "groq");
          knowledgeBase.setVectorMetaData(JsonUtils.convertToJSON(vectorMetaData));
          knowledgeBase.setDocId("[]");
          knowledgeBase.setCollectionName(dto.getCollectionName());
          log.debug("Collection metadata set for collection: {}", dto.getCollectionName());
        }
        default -> throw new UnsupportedOperationException(
            String.format("provided type %s is invalid", dto.getType()));
      }

      knowledgeBase.setCreatedTime(new Date());
      knowledgeBase.setTag(dto.getTags());
      knowledgeBase.setNanoId(NanoIdUtils.randomNanoId());
      KnowledgeBase saved = knowledgeDao.save(knowledgeBase);
      log.debug("saved successfully knowledge data");
      result.put(NANO_ID, saved.getNanoId());
      result.put(PromptConstants.RESULT, PromptConstants.SUCCESS);
      return result;
    } catch (Exception e) {
      if (e instanceof DataIntegrityViolationException) {
        throw new DuplicateResourceException("KnowledgeBase already exists: " + dto.getName());
      }
      log.error("Error while saving knowledge base: {}", e.getMessage(), e);
      throw new BusinessException("Failed to save knowledge base");
    }
  }

  private List<Document> getContentFromDocx(ByteArrayResource byteArrayResource) {
    log.debug("Starting to read DOCX");
    try (InputStream inputStream = byteArrayResource.getInputStream()) {
      com.aspose.words.Document document = new com.aspose.words.Document(inputStream);
      StringBuilder fullText = new StringBuilder();
      fullText = getFullText(document, fullText);
      log.debug("Successfully read DOCX content using Aspose Words");
      return chunkAndTokenize(fullText.toString(), null);
    } catch (Exception e) {
      log.error("Error reading DOCX file using Aspose Words: {}", e.getMessage(), e);
      throw new BusinessException("Error reading DOCX file: " + e.getMessage(), e);
    }
  }

  private static StringBuilder getFullText(
      com.aspose.words.Document document, StringBuilder fullText) {
    log.debug("Starting to extract text from DOCX using Aspose Words");
    try {
      // Iterate through all nodes in the document
      for (Object nodeObj : document.getChildNodes(NodeType.ANY, true)) {
        if (nodeObj instanceof Node node) {
          if (node instanceof Paragraph paragraph) {
            appendParagraphText(paragraph, fullText);
          } else if (node instanceof Table table) {
            appendTableText(table, fullText);
          }
        }
      }
      log.debug("Successfully extracted text from DOCX using Aspose Words");
      return fullText;
    } catch (Exception e) {
      throw new BusinessException("Failed to extract text from DOCX", e);
    }
  }

  private static void appendParagraphText(Paragraph paragraph, StringBuilder fullText) {
    if (isPageBreak(paragraph)) {
      fullText.append("\n");
      return;
    }
    String text = paragraph.getText().trim();
    if (!text.isEmpty()) {
      fullText.append(text).append("\n");
    }
  }

  private static void appendTableText(Table table, StringBuilder fullText) {
    for (Row row : table.getRows()) {
      for (Cell cell : row.getCells()) {
        String cellText = cell.getText().trim();
        if (!cellText.isEmpty()) {
          fullText.append(cellText).append(" | ");
        }
      }
      fullText.append("\n");
    }
  }

  // Check if paragraph is a heading (page break indicator)
  private static boolean isPageBreak(Paragraph paragraph) {
    try {
      String styleName = paragraph.getParagraphFormat().getStyle().getName();
      return styleName != null && styleName.toLowerCase().contains("heading");
    } catch (Exception e) {
      return false;
    }
  }

  private static Set<String> extractFileNames(List<MultipartFile> files) {
    return files.stream()
        // Extract the file name
        .map(MultipartFile::getOriginalFilename)
        // Ensure no null values
        .filter(Objects::nonNull)
        // Collect into a list
        .collect(Collectors.toSet());
  }

  private static List<String> doChunk(String input, int chunkSize) {
    List<String> chunks = new ArrayList<>();
    if (input == null || input.isEmpty()) {
      return chunks;
    }
    int length = input.length();
    for (int i = 0; i < length; i += chunkSize) {
      chunks.add(input.substring(i, Math.min(length, i + chunkSize)));
    }
    return chunks;
  }

  private List<Document> getContentFromPdf(ByteArrayResource resource) {
    log.debug("Starting to read PDF content using Aspose");
    try (InputStream inputStream = resource.getInputStream()) {
      com.aspose.pdf.Document pdfDocument = new com.aspose.pdf.Document(inputStream);
      TextAbsorber textAbsorber = new TextAbsorber();
      pdfDocument.getPages().accept(textAbsorber);
      String text = textAbsorber.getText();
      log.debug("Successfully extracted text from PDF using Aspose");
      return chunkAndTokenize(text, null);
    } catch (Exception e) {
      log.error("Error extracting text from PDF using Aspose: {}", e.getMessage(), e);
      throw new BusinessException("Error reading PDF file: " + e.getMessage(), e);
    }
  }

  private List<Document> chunkAndTokenize(String fullText, String metadata) {
    log.debug("Starting to chunk and tokenize content");
    // 1. Chunk by ~2000 characters
    List<String> charChunks = doChunk(fullText, DEFAULT_CHUNK_SIZE);

    // 2. Recursively split each by 400-token max
    return charChunks.parallelStream()
        .flatMap(chunk -> splitRecursively(chunk, MAX_TOKENS_PER_CHUNK).stream())
        .map(chunk -> metadata != null ? createDoc(chunk, metadata) : new Document(chunk))
        .toList();
  }

  private static int findNearestWhitespace(String text, int around) {
    int left = around;
    int right = around;

    while (left > 0 || right < text.length()) {
      if (left > 0 && Character.isWhitespace(text.charAt(left))) {
        return left;
      }
      if (right < text.length() && Character.isWhitespace(text.charAt(right))) {
        return right;
      }
      left--;
      right++;
    }

    log.debug("Successfully found nearest whitespace");
    return around; // fallback
  }

  private List<String> splitRecursively(String content, int maxTokens) {
    List<String> results = new ArrayList<>();
    TokenCountEstimator estimator = new JTokkitTokenCountEstimator(EncodingType.P50K_BASE);
    int tokenCount = estimator.estimate(content);

    if (tokenCount <= maxTokens) {
      results.add(content.trim().replace("...", ""));
      return results;
    }

    // Split at nearest whitespace around the middle
    int mid = content.length() / MID_DIVISOR;
    int splitIndex = findNearestWhitespace(content, mid);
    if (splitIndex <= 0 || splitIndex >= content.length()) {
      // Fallback: can't split properly
      results.add(content.trim());
      return results;
    }

    String firstHalf = content.substring(0, splitIndex).trim();
    String secondHalf = content.substring(splitIndex).trim();

    // Recursively split each half
    results.addAll(splitRecursively(firstHalf, maxTokens));
    results.addAll(splitRecursively(secondHalf, maxTokens));
    log.debug("Content and tokens split successfully");
    return results;
  }

  private static ByteArrayResource getByteArrayResources(MultipartFile files) {
    try {
      log.debug("Converting file to ByteArrayResource: {}", files.getOriginalFilename());
      return new ByteArrayResource(files.getBytes());
    } catch (IOException e) {
      log.error("Error converting file: {}", files.getOriginalFilename(), e);
      return null;
    }
  }

  @Override
  public Long count(String filter) {
    log.debug("Inside @method count Knowledge Base");
    return customFilter.countByFilter(KnowledgeBase.class, filter);
  }

  @Override
  public List<KnowledgeBaseResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.debug("Inside @method search Knowledge Base");
    try {
      List<KnowledgeBase> knowledgeBases =
          customFilter.searchByFilter(
              KnowledgeBase.class, filter, orderBy, orderType, offset, size);
      return KnowledgeConverter.getKnowledgeBasesDtoList(knowledgeBases);
    } catch (Exception e) {
      log.error("Error message : {}", e.getMessage(), e);
      throw new BusinessException(e.getMessage(), e);
    }
  }

  @Override
  public Map<String, String> updateDocument(String nanoId, DocumentRequestDto dto)
      throws JsonProcessingException {
    log.debug("Inside @class KnowledgeBaseServiceImpl @method updateDocument");
    Map<String, String> result = new HashMap<>();
    List<List<Document>> documents = new ArrayList<>();
    List<String> s3StorageFileNames = new ArrayList<>();

    KnowledgeBase existingKnowledgeBase =
        knowledgeDao
            .findByNanoId(nanoId)
            .orElseThrow(
                () -> {
                  log.warn(
                      "Inside @method updateDocument , KnowledgeBase not found for nanoId: {}",
                      nanoId);
                  return new ResourceNotFoundException("KnowledgeBase not found");
                });
    try {
      mapToEntity(dto, existingKnowledgeBase);
      String oldFilter = existingKnowledgeBase.getFilter();

      switch (dto.getType()) {
        case "DOCUMENT" -> {
          List<MultipartFile> files = dto.getFile();
          Set<String> fileNames = extractFileNames(files);
          for (MultipartFile file : files) {
            fileNames.add(file.getOriginalFilename());
            List<Document> content = extractContentFromFile(file);
            if (content != null && !content.isEmpty()) {
              documents.add(content);
            }
          }

          existingKnowledgeBase.setFileName(String.join(", ", fileNames));
          VectorResponseDto vectorResponse = saveInVectorFromDocReader(documents);
          log.info("Going to update file on s3service");
          s3StorageFileNames =
              s3Service.uploadFileToS3(
                  files, vectorResponse.getFilter(), existingKnowledgeBase.getS3FileNames());
          existingKnowledgeBase.setS3FileNames(JsonUtils.convertToJSON(s3StorageFileNames));
          existingKnowledgeBase.setFilter(vectorResponse.getFilter());
          existingKnowledgeBase.setDocMetaData(
              JsonUtils.convertToJSON(vectorResponse.getMetadata()));
          existingKnowledgeBase.setDocId(JsonUtils.convertToJSON(vectorResponse.getDocIds()));

          log.debug("Updated document content for ID: {}", nanoId);
        }
        case "SQL" -> {
          List<String> tableNames = dto.getTables();
          List<String> docIds = new ArrayList<>();
          for (String tableName : tableNames) {
            Map<String, Object> docMetaData = Map.of(KNOWLEDGE_BASE_FILTER_KEY, tableName);
            String tableSchema = apiService.getTableSchema(dto.getIntegration(), tableName);
            Document document = new Document(tableSchema);
            document.getMetadata().putAll(docMetaData);
            docIds.add(document.getId());
            vectorStore.accept(List.of(document));
            log.debug("Document is successfully save in vector. table : {}", tableName);
          }
          existingKnowledgeBase.setDocId(JsonUtils.convertToJSON(docIds));
          existingKnowledgeBase.setDescription(
              String.format(
                  """
                                            Tool is designed to assist an LLM in constructing valid SQL queries by providing the schema details of a specified database table.
                                            Given a table name as input, this tool retrieves and returns its schema, including column names, data types, constraints, and other relevant metadata.
                                            It contains schema for following tables:
                                            %s
                                            """,
                  tableNames));
          existingKnowledgeBase.setIntegration(dto.getIntegration());
          existingKnowledgeBase.setTables(JsonUtils.convertToJSON(tableNames));
        }
        case "WEBSITE" -> {
          processWebsiteData(dto, existingKnowledgeBase);
          log.debug("Updated website details for ID: {}", nanoId);
        }
        case "COLLECTION" -> {
          Map<String, String> vectorMetaData =
              Map.of(
                  PromptConstants.VECTOR_DATABASE,
                  vectorDatabase,
                  PromptConstants.INDEX_NAME,
                  indexName,
                  PromptConstants.COLLECTION_NAME,
                  dto.getCollectionName(),
                  PromptConstants.EMBEDDING_MODEL_KEY,
                  PromptConstants.EMBEDDING_MODEL,
                  PromptConstants.CHAT_MODEL_KEY,
                  PromptConstants.CHAT_MODEL_VALUE,
                  PromptConstants.PROVIDER,
                  "groq");
          existingKnowledgeBase.setVectorMetaData(JsonUtils.convertToJSON(vectorMetaData));
          existingKnowledgeBase.setDocId("[]");
          existingKnowledgeBase.setCollectionName(dto.getCollectionName());
          existingKnowledgeBase.setTag(dto.getTags());
          log.debug("Updated collection metadata");
        }
        default -> throw new UnsupportedOperationException(
            String.format("provided type %s is invalid", dto.getType()));
      }

      // Need to remove this line after successful data migration to redis
      oldFilter = oldFilter.replace("-", "");
      // Delete old vector data before saving the new file content
      Filter.Expression expression =
          new Filter.Expression(
              Filter.ExpressionType.EQ,
              new Filter.Key(KNOWLEDGE_BASE_FILTER_KEY),
              new Filter.Value(oldFilter));
      for (int i = 0; i < PromptConstants.VECTOR_STORE_DELETION_BATCH_SIZE; i++) {
        vectorStore.delete(expression);
      }

      knowledgeDao.save(existingKnowledgeBase);
      log.debug("Updated successfully knowledge data");
      result.put(PromptConstants.RESULT, PromptConstants.SUCCESS);
      return result;
    } catch (Exception e) {
      if (e instanceof DataIntegrityViolationException) {
        throw new DuplicateResourceException("KnowledgeBase already exists: " + dto.getName());
      }
      log.error("Error while updating knowledge base: {}", e.getMessage(), e);
      throw new BusinessException("Failed to update knowledge base");
    }
  }

  private List<Document> extractContentFromFile(MultipartFile file) throws IOException {
    log.debug("Inside method extractContentFromFile: {}", file.getOriginalFilename());
    String fileType = getFileExtension(file.getOriginalFilename());

    return switch (fileType.toLowerCase()) {
      case "pdf" -> extractContentFromPDF(file);
      case "docx" -> extractContentFromWord(file);
      case "xlsx", "xls" -> extractContentFromExcel(file);
      default -> throw new IllegalArgumentException("Unsupported file type: " + fileType);
    };
  }

  private String formatRow(List<String> row, List<Integer> maxColWidths) {
    StringJoiner joiner = new StringJoiner(" | ");
    for (int i = 0; i < row.size(); i++) {
      // Ensure minimum width
      int width = Math.max(1, maxColWidths.get(i));
      String format = "%-" + width + "s";
      String padded = String.format(format, row.get(i));
      joiner.add(padded);
    }
    return joiner.toString();
  }

  private static Document createDoc(String text, String sheetName) {
    Document doc = new Document(text);
    doc.getMetadata().put("sheetname", sheetName);
    return doc;
  }

  private List<Document> extractContentFromPDF(MultipartFile file) throws IOException {
    ByteArrayResource resource = getByteArrayResources(file);
    log.debug("Inside method extractContentFromPDF");
    return getContentFromPdf(resource);
  }

  private List<Document> extractContentFromWord(MultipartFile file) throws IOException {
    ByteArrayResource resource = getByteArrayResources(file);
    log.debug("Inside method extractContentFromWord");
    return getContentFromDocx(resource);
  }

  private List<Document> extractContentFromExcel(MultipartFile file) throws IOException {
    log.debug("Extracting content from Excel file: {}", file.getOriginalFilename());

    try (InputStream inputStream = file.getInputStream()) {
      Workbook workbook = new Workbook(inputStream);
      WorksheetCollection worksheets = workbook.getWorksheets();

      // Process sheets in parallel
      return IntStream.range(0, worksheets.getCount())
          .parallel()
          .mapToObj(i -> processSheet(worksheets.get(i)))
          .flatMap(List::stream)
          .toList();
    } catch (Exception e) {
      log.error("Failed to extract content from Excel file: {}", file.getOriginalFilename(), e);
      throw new IOException("Error reading Excel file", e);
    }
  }

  private List<Document> processSheet(Worksheet worksheet) {
    log.debug("Processing sheet: {}", worksheet.getName());
    String sheetName = worksheet.getName();
    List<List<String>> rows = new ArrayList<>();
    List<Integer> maxColWidths = new ArrayList<>();

    Cells cells = worksheet.getCells();
    int maxRow = cells.getMaxDataRow();
    int maxCol = cells.getMaxDataColumn();

    for (int rowIndex = 0; rowIndex <= maxRow; rowIndex++) {
      List<String> cellValues = new ArrayList<>();
      for (int colIndex = 0; colIndex <= maxCol; colIndex++) {
        com.aspose.cells.Cell cell = cells.get(rowIndex, colIndex);
        String value = getFormattedCellValue(cell);
        cellValues.add(value);

        while (maxColWidths.size() <= colIndex) {
          maxColWidths.add(0);
        }
        maxColWidths.set(colIndex, Math.max(maxColWidths.get(colIndex), value.length()));
      }
      rows.add(cellValues);
    }

    // Format all text in one go
    StringBuilder fullText = new StringBuilder();
    for (List<String> row : rows) {
      fullText.append(formatRow(row, maxColWidths)).append("\n");
    }

    // First chunk by 2000 characters
    List<String> charChunks = doChunk(fullText.toString(), DEFAULT_CHUNK_SIZE);

    log.debug("Successfully chunked content from Excel file: {}", sheetName);

    // Then split each by 400-token max
    return charChunks.parallelStream()
        .flatMap(charChunk -> splitRecursively(charChunk, MAX_TOKENS_PER_CHUNK).stream())
        .map(chunk -> createDoc(chunk, sheetName))
        .toList();
  }

  private String getFormattedCellValue(com.aspose.cells.Cell cell) {
    if (cell == null) {
      return "";
    }
    try {
      String formatted = cell.getStringValue();
      if (formatted != null && !formatted.trim().isEmpty()) {
        return formatted.trim();
      }
      Object value = cell.getValue();
      switch (value) {
        case null -> {
          return "";
        }
        case Double num -> {
          boolean isInteger = num == Math.floor(num);
          return isInteger ? String.format("%.0f", num) : String.valueOf(num);
        }
        case Boolean ignored -> {
          return value.toString();
        }
        case DateTime dateTime -> {
          // Or format it as needed
          return dateTime.toDate().toString();
        }
        default -> {
          return value.toString().trim();
        }
      }
    } catch (Exception e) {
      log.warn("Error getting cell value: {}", e.getMessage());
      return "";
    }
  }

  @Override
  public Map<String, String> softDelete(String id) {
    log.debug("Inside @method softDelete @param id :{} ", id);
    Map<String, String> result = new HashMap<>();
    try {
      Optional<KnowledgeBase> knowledgeBaseOpt = knowledgeDao.findByNanoId(id);
      if (knowledgeBaseOpt.isPresent()) {
        KnowledgeBase knowledgeBase = knowledgeBaseOpt.get();
        customFilter.verifyCreator(customerInfo.getUserId(), knowledgeBase.getCreator());
        knowledgeBase.setDeleted(true);
        knowledgeDao.save(knowledgeBase);
        result.put(PromptConstants.RESULT, PromptConstants.SUCCESS);
        log.info("Soft deleted KnowledgeBase with id: {}", id);
      } else {
        result.put(PromptConstants.RESULT, PromptConstants.FAILED);
        log.warn("KnowledgeBase not found for ID: {}", id);
      }
    } catch (AccessDeniedException ade) {
      throw ade; // Let global handler return 403
    } catch (Exception ex) {
      result.put(PromptConstants.RESULT, PromptConstants.FAILED);
      log.error("Error occurred while soft deleting KnowledgeBase with id: {}", id, ex);
    }
    return result;
  }

  @Override
  public List<KnowledgeBaseSdkDto> getKnowledgeBaseByIds(List<String> ids) {
    log.debug("Inside @method getKnowledgeBaseByIds @param ids : {} ", ids);
    List<KnowledgeBase> knowledgeBases = knowledgeDao.findAllByNanoIds(ids);
    return SdkUtils.getKnowledgeBaseSdkDtoList(knowledgeBases);
  }

  private void mapToEntity(DocumentRequestDto dto, KnowledgeBase knowledgeBase)
      throws JsonProcessingException {
    log.debug("Inside method mapToEntity");
    try {
      knowledgeBase.setIsContext(false);
      knowledgeBase.setType(dto.getType());
      knowledgeBase.setName(dto.getName());
      knowledgeBase.setDocType(dto.getDocType());
      knowledgeBase.setDescription(dto.getDescription());
      knowledgeBase.setSimilarityThreshold(dto.getSimilarityThreshold());
      knowledgeBase.setTopK(dto.getTopK());
      Map<String, String> vectorMetaData =
          Map.of(
              PromptConstants.VECTOR_DATABASE,
              vectorDatabase,
              PromptConstants.INDEX_NAME,
              indexName,
              PromptConstants.PREFIX,
              redisPrefix,
              PromptConstants.EMBEDDING_MODEL_KEY,
              PromptConstants.EMBEDDING_MODEL,
              PromptConstants.CHAT_MODEL_KEY,
              PromptConstants.CHAT_MODEL_VALUE,
              PromptConstants.PROVIDER,
              "groq");
      log.debug("Successfully load vectorMetaData");
      if (dto.getDeleted() == null) {
        knowledgeBase.setDeleted(false);
      } else {
        knowledgeBase.setDeleted(dto.getDeleted());
      }
      knowledgeBase.setVectorMetaData(JsonUtils.convertToJSON(vectorMetaData));
      knowledgeBase.setReturnDirect(false);
      knowledgeBase.setModifiedTime(new Date());
    } catch (Exception e) {
      throw new BusinessException("Error in map to entity" + e.getMessage() + e);
    }
    log.debug("Mapped document request data to knowledge base entity");
  }

  public VectorResponseDto saveInVector(List<String> contents) {
    String filter = UUID.randomUUID().toString();
    filter = filter.replace("-", "");
    Map<String, Object> docMetaData = Map.of(KNOWLEDGE_BASE_FILTER_KEY, filter);
    List<String> docIds = new ArrayList<>();

    for (String content : contents) {
      List<String> chunks = doChunk(content, LARGE_CHUNK_SIZE);
      List<Document> documents = new ArrayList<>();

      for (String chunk : chunks) {
        Document document = new Document(chunk);
        document.getMetadata().putAll(docMetaData);
        documents.add(document);
        docIds.add(document.getId());
      }
      vectorStore.accept(documents);
    }
    log.debug("Document is successfully save in vector");
    return new VectorResponseDto(filter, docMetaData, docIds);
  }

  public VectorResponseDto saveInVectorFromDocReader(List<List<Document>> documentList) {
    String filter = UUID.randomUUID().toString();
    filter = filter.replace("-", "");
    Map<String, Object> docMetaData = Map.of(KNOWLEDGE_BASE_FILTER_KEY, filter);
    List<String> docIds = new ArrayList<>();
    processDocuments(documentList, docMetaData, docIds);
    log.debug("Successfully save in vector");
    return new VectorResponseDto(filter, docMetaData, docIds);
  }

  public void saveInVectorFromCsv(List<String> contents) {
    log.debug("Inside @method saveInVectorFromCsv");
    Map<String, Object> docMetaData = Map.of(CUSTOM_AGENT_FILTER_KEY, CUSTOM_AGENT_FILTER);

    List<Document> batch = new ArrayList<>();

    for (String content : contents) {
      Document doc = new Document(content);
      doc.getMetadata().putAll(docMetaData);
      batch.add(doc);
      // save BATCH_SIZE records at a time
      if (batch.size() == BATCH_SIZE) {
        log.debug("saving batch of {} records", BATCH_SIZE);
        vectorStore.accept(new ArrayList<>(batch));
        batch.clear();
      }
    }

    // Handle remaining documents
    if (!batch.isEmpty()) {
      vectorStore.accept(batch);
    }

    log.debug("successfully save csv contents in vector");
  }

  private void processDocuments(
      List<List<Document>> documentList, Map<String, Object> docMetaData, List<String> docIds) {
    List<Document> batch = new ArrayList<>();
    for (List<Document> documents : documentList) {
      for (Document document : documents) {
        document.getMetadata().putAll(docMetaData);
        batch.add(document);
        docIds.add(document.getId());
        // If the batch size reaches the limit, process it
        if (batch.size() == BATCH_SIZE) {
          // Process the batch
          vectorStore.accept(new ArrayList<>(batch));
          // Clear the batch for the next set
          batch.clear();
        }
      }
    }
    // Process any remaining documents that didn't complete a full batch
    if (!batch.isEmpty()) {
      vectorStore.accept(batch);
    }
    log.debug("successfully Processed documents");
  }

  @Override
  public Map<String, String> updateTagById(String id, Map<String, String> tags) {
    String newTag = tags.get("tags");
    log.debug("inside @method updateTagById. @param  : id -> {} tags : {}", id, newTag);
    KnowledgeBase knowledgeBase =
        knowledgeDao
            .findByNanoId(id)
            .orElseThrow(
                () -> {
                  log.warn(
                      "Inside @method updateTagById , Knowledge base not found for nanoId: {}", id);
                  return new BusinessException("knowledge base is not found");
                });
    try {
      knowledgeBase.setTag(newTag);
      knowledgeDao.save(knowledgeBase);
      return Map.of(APIConstants.RESULT, APIConstants.SUCCESS);
    } catch (Exception e) {
      log.error("error while updating tag of knowledge base : {}", e.getMessage(), e);
      throw new BusinessException("Failed to update tag");
    }
  }

  @Override
  public List<String> getTablesName(String name) {
    log.debug("Inside @method getTablesName with name: {}", name);
    return apiService.getTablesName(name);
  }

  private String getFileExtension(String filename) {
    if (filename == null || !filename.contains(".")) {
      throw new IllegalArgumentException("Invalid file name: " + filename);
    }
    return filename.substring(filename.lastIndexOf('.') + 1);
  }

  @Override
  public Map<String, Object> existsWebsiteUrl(String webSiteUrl) {
    log.debug("Checking existence of website URL: {}", webSiteUrl);
    Map<String, Object> result = new HashMap<>();
    try {
      List<KnowledgeBase> knowledgeBaseList = knowledgeDao.existsWebsiteUrl(webSiteUrl);
      boolean exists = !knowledgeBaseList.isEmpty();
      result.put("exists", exists);
      if (exists) {
        List<String> ids = knowledgeBaseList.stream().map(KnowledgeBase::getNanoId).toList();
        result.put("nanoIds", ids);
        log.info("Website URL: {} exists with IDs: {}", webSiteUrl, ids);
      }
    } catch (Exception e) {
      log.error("Error while checking website URL: {} - {}", webSiteUrl, e.getMessage(), e);
      throw new BusinessException("Failed to check website URL existence");
    }
    return result;
  }

  @Override
  public Map<String, String> saveContent(DocumentRequestDto dto) {
    log.debug("Inside @class KnowledgeBaseServiceImpl @method saveContent");
    KnowledgeBase knowledgeBase = new KnowledgeBase();
    Map<String, String> result = new HashMap<>();
    try {
      mapToEntity(dto, knowledgeBase);
      VectorResponseDto vectorResponse = saveInVector(List.of(dto.getContent()));
      knowledgeBase.setFilter(vectorResponse.getFilter());
      knowledgeBase.setDocMetaData(JsonUtils.convertToJSON(vectorResponse.getMetadata()));
      knowledgeBase.setDocId(JsonUtils.convertToJSON(vectorResponse.getDocIds()));
      knowledgeBase.setTag(dto.getTags());
      knowledgeBase.setCreatedTime(new Date());
      knowledgeBase.setNanoId(NanoIdUtils.randomNanoId());
      knowledgeBase = knowledgeDao.save(knowledgeBase);
      log.debug("method saveContent , saved successfully knowledge data");
      result.put(PromptConstants.RESULT, PromptConstants.SUCCESS);
      result.put(NANO_ID, knowledgeBase.getNanoId());
      return result;
    } catch (Exception e) {
      if (e instanceof DataIntegrityViolationException) {
        throw new DuplicateResourceException("KnowledgeBase already exists: " + dto.getName());
      }
      if (e instanceof BusinessException) {
        log.warn("Unable to map knowledge base: {}", e.getMessage(), e);
      }
      log.error("Error while saving knowledge base: {}", e.getMessage(), e);
      throw new BusinessException("Failed saveContent for knowledge base");
    }
  }

  @Override
  public ResponseEntity<String> milvusImportCsv(MultipartFile file) {
    log.debug("Inside @method milvusImportCsv");
    try (CSVReader csvReader =
        new CSVReader(new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
      List<String[]> rows = csvReader.readAll();
      if (rows.isEmpty()) {
        return ResponseEntity.badRequest().body("CSV file is empty.");
      }
      // Find "content" column index
      int contentIndex = Arrays.asList(rows.get(0)).indexOf("content");
      if (contentIndex == -1) {
        return ResponseEntity.badRequest().body("CSV file does not contain 'content' column.");
      }
      List<String> contents =
          rows.stream()
              // Skip header row
              .skip(1)
              .map(row -> row[contentIndex])
              .filter(content -> content != null && !content.isEmpty())
              // Use `toList()` for an immutable list (Java 16+)
              .toList();

      if (contents.isEmpty()) {
        return ResponseEntity.badRequest().body("No valid content found in CSV.");
      }

      log.debug("Successfully extracted {} content records from CSV.", contents.size());
      // Delete old records before saving new content
      Filter.Expression expression =
          new Filter.Expression(
              Filter.ExpressionType.EQ,
              new Filter.Key(CUSTOM_AGENT_FILTER_KEY),
              new Filter.Value(CUSTOM_AGENT_FILTER));
      for (int i = 0; i < PromptConstants.CUSTOM_AGENT_DELETION_BATCH_SIZE; i++) {
        vectorStore.delete(expression);
      }
      saveInVectorFromCsv(contents);
      return ResponseEntity.ok("CSV data imported successfully.");

    } catch (Exception e) {
      log.error("Error processing CSV file: ", e);
      return ResponseEntity.internalServerError().body("Failed to process CSV file.");
    }
  }

  @Override
  public Map<String, String> getAnswer(String knowledgeBaseName, String userQuestion) {
    log.debug(
        "Inside @class KnowledgeBaseServiceImpl @method getAnswer with knowledgeBaseName: {}",
        knowledgeBaseName);
    try {
      // 1. Retrieve knowledge base
      KnowledgeBase knowledgeBase =
          Optional.ofNullable(knowledgeDao.findByName(knowledgeBaseName))
              .orElseThrow(
                  () -> {
                    log.warn("Knowledge base not found for name: {}", knowledgeBaseName);
                    return new NoSuchElementException("Knowledge base not found");
                  });

      // 2. Parse vector metadata
      Map<String, String> vectorMetaData =
          JsonUtils.convertJsonToObject(knowledgeBase.getVectorMetaData(), Map.class);
      String provider = vectorMetaData.get(PromptConstants.PROVIDER);
      String model = vectorMetaData.get(PromptConstants.CHAT_MODEL_KEY);

      OpenAiChatOptions chatOptions = OpenAiChatOptions.builder().model(model).build();
      log.debug("Chat options initialized for provider: {} and model: {}", provider, model);

      // 4. Build search request
      SearchRequest searchRequest =
          SearchRequest.builder()
              .similarityThreshold(knowledgeBase.getSimilarityThreshold())
              .topK(knowledgeBase.getTopK())
              .filterExpression(
                  knowledgeBase.getFilter() != null
                      ? String.format("doc_id == '%s'", knowledgeBase.getFilter())
                      : null)
              .build();
      log.debug(
          "Search request initialized with topK: {} and threshold: {}",
          knowledgeBase.getTopK(),
          knowledgeBase.getSimilarityThreshold());

      // 5. Generate answer
      ChatResponse chatResponse =
          ChatClient.builder(inferenceManager.getChatModelByProvider(provider))
              .build()
              .prompt()
              .user(userQuestion)
              .options(chatOptions)
              .advisors(new QuestionAnswerAdvisor(vectorStore, searchRequest))
              .call()
              .chatResponse();

      String answer = chatResponse.getResult().getOutput().getText();
      log.info("Successfully generated answer for question ---> {}  ", userQuestion);

      return Map.of("answer", answer);

    } catch (Exception e) {
      log.error("Error in getAnswer: {}", e.getMessage(), e);
      throw new BusinessException("Failed to process request due to an error");
    }
  }

  @Override
  public ResponseEntity<Resource> exportKnowledgeBases(List<String> ids) {
    log.debug("Inside method exportKnowledgeBases");
    List<KnowledgeBase> knowledgeBaseList = knowledgeDao.findAllByNanoIds(ids);
    knowledgeBaseList.removeIf(kb -> !Boolean.FALSE.equals(kb.getDeleted()));
    return getResponse("knowledge-bases", knowledgeBaseList);
  }

  private static ResponseEntity<Resource> getResponse(
      String fileName, List<KnowledgeBase> knowledgeBaseList) {
    log.debug("Inside method getResponse for KnowledgeBase export");
    List<Function<KnowledgeBase, Object>> fieldExtractors = getCombinedFunctionList();
    log.debug("Going to export knowledge bases as csv");
    return CSVUtils.exportCSV(knowledgeBaseList, csvColumnHeader, fileName, fieldExtractors);
  }

  private static List<Function<KnowledgeBase, Object>> getCombinedFunctionList() {
    try {
      log.debug("Inside method getCombinedFunctionList");
      List<Function<KnowledgeBase, Object>> combined = new ArrayList<>();
      combined.addAll(getFunctionListPart1());
      combined.addAll(getFunctionListPart2());
      log.debug("Successfully combined function lists");
      return combined;
    } catch (Exception e) {
      throw new BusinessException("Error in getCombinedFunctionList: " + e.getMessage(), e);
    }
  }

  private static List<Function<KnowledgeBase, Object>> getFunctionListPart1() {
    log.debug("Inside method getFunctionListPart1");
    return List.of(
        KnowledgeBase::getName,
        KnowledgeBase::getDescription,
        KnowledgeBase::getVectorMetaData,
        KnowledgeBase::getTopK,
        KnowledgeBase::getSimilarityThreshold,
        KnowledgeBase::getType,
        kb -> kb.getWebSiteUrl() == null ? "NULL" : kb.getWebSiteUrl(),
        kb -> kb.getFileName() == null ? "NULL" : kb.getFileName(),
        kb -> kb.getWebsiteTaskStatus() == null ? "NULL" : kb.getWebsiteTaskStatus(),
        kb -> kb.getWebsiteTaskId() == null ? "NULL" : kb.getWebsiteTaskId(),
        kb -> kb.getWebsiteTaskError() == null ? "NULL" : kb.getWebsiteTaskError());
  }

  private static List<Function<KnowledgeBase, Object>> getFunctionListPart2() {
    log.debug("Inside method getFunctionListPart2");
    return List.of(
        kb -> kb.getCollectionName() == null ? "NULL" : kb.getCollectionName(),
        kb -> kb.getDocType() == null ? "NULL" : kb.getDocType(),
        kb -> kb.getDocId() == null ? "NULL" : kb.getDocId(),
        kb -> kb.getFilter() == null ? "NULL" : kb.getFilter(),
        kb -> kb.getDocMetaData() == null ? "NULL" : kb.getDocMetaData(),
        KnowledgeBase::getIsContext,
        kb -> kb.getTables() == null ? "NULL" : kb.getTables(),
        kb -> kb.getIntegration() == null ? "NULL" : kb.getIntegration(),
        KnowledgeBase::getReturnDirect);
  }

  @Override
  public ResponseEntity<Resource> importKnowledgeBases(MultipartFile file) {
    log.debug("Inside importKnowledgeBases method");
    int maxRecords = MAX_IMPORT_RECORDS;
    try {
      Function<CSVRecord, KnowledgeBase> recordTransformer =
          csvRecord -> {
            KnowledgeBase knowledgeBase = new KnowledgeBase();

            knowledgeBase.setName(csvRecord.get("Name"));
            knowledgeBase.setDescription(csvRecord.get("Description"));
            knowledgeBase.setVectorMetaData(csvRecord.get("VectorMetaData"));
            knowledgeBase.setTopK(Integer.valueOf(csvRecord.get("TopK")));
            knowledgeBase.setSimilarityThreshold(
                Double.valueOf(csvRecord.get("SimilarityThreshold")));
            knowledgeBase.setType(csvRecord.get("Type"));
            knowledgeBase.setWebSiteUrl(csvRecord.get("webSiteUrl"));
            knowledgeBase.setFileName(csvRecord.get("fileName"));
            knowledgeBase.setWebsiteTaskStatus(csvRecord.get("websiteTaskStatus"));
            knowledgeBase.setWebsiteTaskId(csvRecord.get("websiteTaskId"));
            knowledgeBase.setWebsiteTaskError(csvRecord.get("websiteTaskError"));
            knowledgeBase.setCollectionName(csvRecord.get(PromptConstants.COLLECTION_NAME));
            knowledgeBase.setDocType(csvRecord.get("docType"));
            knowledgeBase.setDocId(csvRecord.get("docId"));
            knowledgeBase.setFilter(csvRecord.get("filter"));
            knowledgeBase.setDocMetaData(csvRecord.get("docMetaData"));
            knowledgeBase.setIsContext(Boolean.valueOf(csvRecord.get("isContext")));
            knowledgeBase.setTables(csvRecord.get("tables"));
            knowledgeBase.setIntegration(csvRecord.get("integration"));
            knowledgeBase.setReturnDirect(Boolean.valueOf(csvRecord.get("returnDirect")));
            return knowledgeBase;
          };

      String exportedFileName = file.getOriginalFilename().replaceAll("\\.[^.]*$", "");
      log.info("Importing knowledge bases from file: {}", exportedFileName);
      return CSVUtils.importCSV(
          file,
          csvColumnHeader,
          recordTransformer,
          this::saveOrUpdateKnowledgeBase,
          exportedFileName,
          maxRecords);

    } catch (IOException | BusinessException e) {
      log.error("Error during knowledge base import: {}", e.getMessage(), e);
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body(new ByteArrayResource(e.getMessage().getBytes()));
    }
  }

  @Override
  public Map<String, String> updateReturnDirect(String id, Boolean returnDirectValue) {
    log.debug(
        "Inside @method updateReturnDirect with id: {} and returnDirectValue: {}",
        id,
        returnDirectValue);

    KnowledgeBase knowledgeBase =
        knowledgeDao
            .findByNanoId(id)
            .orElseThrow(() -> new BusinessException("Knowledge base not found"));
    try {
      knowledgeBase.setReturnDirect(returnDirectValue);
      knowledgeDao.save(knowledgeBase);
      log.debug("Successfully updated return direct flag for knowledge base with id: {}", id);
      return Map.of(APIConstants.RESULT, APIConstants.SUCCESS);
    } catch (Exception e) {
      log.error("Error while updating return direct flag: {}", e.getMessage(), e);
    }
    return Map.of(APIConstants.RESULT, APIConstants.FAILED);
  }

  @Override
  public Map<String, String> saveWebSite(DocumentRequestDto documentDto) {
    log.debug("Inside @method saveWebSite with documentDto: {}", documentDto);
    KnowledgeBase knowledgeBase = new KnowledgeBase();
    Map<String, String> result = new HashMap<>();
    try {
      mapToEntity(documentDto, knowledgeBase);

      // Process website-specific data
      processWebsiteData(documentDto, knowledgeBase);

      // Set common properties
      knowledgeBase.setCreatedTime(new Date());
      knowledgeBase.setTag(documentDto.getTags());
      knowledgeBase.setNanoId(NanoIdUtils.randomNanoId());

      // Save to database
      KnowledgeBase saved = knowledgeDao.save(knowledgeBase);
      log.debug("Successfully saved website knowledge base data with ID: {}", saved.getId());

      // Prepare result
      result.put(NANO_ID, saved.getNanoId());
      result.put(PromptConstants.RESULT, PromptConstants.SUCCESS);
      return result;
    } catch (Exception e) {
      if (e instanceof DataIntegrityViolationException) {
        throw new DuplicateResourceException("KnowledgeBase already exists: " + documentDto.getName());
      }
      if (e instanceof BusinessException) {
        log.warn("Unable to save website knowledge base: {}", e.getMessage(), e);
      }
      log.error("Error while saving website knowledge base: {}", e.getMessage(), e);
      throw new BusinessException("Failed to save website knowledge base");
    }
  }

  private KnowledgeBase saveOrUpdateKnowledgeBase(KnowledgeBase knowledgeBase) {
    log.debug("Saving or updating knowledge base: {}", knowledgeBase.getName());

    Optional<KnowledgeBase> existingKnowledgeBase =
        Optional.ofNullable(knowledgeDao.findByName(knowledgeBase.getName()));

    if (existingKnowledgeBase.isPresent()) {
      KnowledgeBase existing = existingKnowledgeBase.get();
      mapKnowledgeBase(knowledgeBase, existing);
      existing.setModifiedTime(new Date());
      return knowledgeDao.save(existing);
    } else {
      knowledgeBase.setDeleted(false);
      knowledgeBase.setCreatedTime(new Date());
      knowledgeBase.setNanoId(NanoIdUtils.randomNanoId());
      return knowledgeDao.save(knowledgeBase);
    }
  }

  private void processWebsiteData(DocumentRequestDto dto, KnowledgeBase knowledgeBase) {
    String websiteUrl = dto.getWebsiteUrl();
    try {
      String taskId = apiService.triggerCrawl(websiteUrl);
      knowledgeBase.setDocId("[]");
      knowledgeBase.setWebSiteUrl(websiteUrl);
      knowledgeBase.setWebsiteTaskId(taskId);
      knowledgeBase.setWebsiteTaskStatus("PROCESSING");
      log.debug("Triggered crawl for website: {}", websiteUrl);
    } catch (Exception e) {
      log.error("Error triggering crawl for website {}: {}", websiteUrl, e.getMessage(), e);
      knowledgeBase.setWebsiteTaskStatus("FAILED");
      knowledgeBase.setWebsiteTaskError(e.getMessage());
      throw new BusinessException("Failed to trigger website crawl: " + e.getMessage(), e);
    }
  }

  private void mapKnowledgeBase(KnowledgeBase source, KnowledgeBase target) {
    log.debug("Mapping properties from source to target knowledge base");

    target.setDescription(source.getDescription());
    target.setVectorMetaData(source.getVectorMetaData());
    target.setTopK(source.getTopK());
    target.setSimilarityThreshold(source.getSimilarityThreshold());
    target.setType(source.getType());
    target.setWebSiteUrl(source.getWebSiteUrl());
    target.setFileName(source.getFileName());
    target.setWebsiteTaskStatus(source.getWebsiteTaskStatus());
    target.setWebsiteTaskId(source.getWebsiteTaskId());
    target.setWebsiteTaskError(source.getWebsiteTaskError());
    target.setCollectionName(source.getCollectionName());
    target.setDeleted(source.getDeleted());
    target.setDocType(source.getDocType());
    target.setDocId(source.getDocId());
    target.setFilter(source.getFilter());
    target.setDocMetaData(source.getDocMetaData());
    target.setIsContext(source.getIsContext());
    target.setTables(source.getTables());
    target.setIntegration(source.getIntegration());
    target.setReturnDirect(source.getReturnDirect());
    log.debug("Successfully mapped properties from source to target knowledge base");
  }

  @Override
  public Map<String, Boolean> existsKnowledgeBaseName(String name) {
    log.debug("Checking existence of knowledge base with name: {}", name);
    boolean exists = knowledgeDao.findByName(name) != null;
    log.info("Knowledge base '{}' existence check result: {}", name, exists);
    return Map.of(PromptConstants.RESULT, exists);
  }

  @Override
  public byte[] downloadFileFromS3(String fileName) {
    log.debug(
        "Inside @class KnowledgeBaseServiceImpl @method downloadFileFromGcs with fileName: {}",
        fileName);
    try {
      return s3Service.downloadFileFromS3(fileName);
    } catch (Exception e) {
      log.error("DownloadFileFromGcs Failed to download file: {} , {}  ", e, e.getMessage());
      throw new BusinessException("Failed to download file");
    }
  }
}
