/*
 * Copyright (c) 2024 Vwaves Technologies Private Limited
 *
 * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.
 * Product: Aspose.Total for Java
 * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations
 * Order ID: 250610115636
 *
 * This source code is part of a proprietary software system and may not be
 * distributed, modified, or reused without explicit written permission from
 * Vwaves Technologies Private Limited.
 *
 * Contact: <EMAIL>
 */
package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.enums.AssertionType;
import com.enttribe.promptanalyzer.enums.MatchStrategy;
import com.enttribe.promptanalyzer.model.Assertion;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Data Access Object interface for Assertion entity operations. Provides methods to interact with
 * the Assertion table in the database for managing test assertions.
 *
 * <p>This interface extends JpaRepository to provide standard CRUD operations and includes
 * custom query methods for retrieving assertions by various criteria including nanoId,
 * assertion type, and match strategy.</p>
 *
 * @see Assertion
 * @see JpaRepository
 * @see AssertionType
 * @see MatchStrategy
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public interface AssertionDao extends JpaRepository<Assertion, Long> {

  /**
   * Finds an assertion by its nanoId that is not deleted.
   *
   * @param nanoId the nanoId to search for
   * @return an Optional containing the assertion if found and not deleted, empty otherwise
   */
  @Query("SELECT a FROM Assertion a WHERE a.nanoId = :nanoId AND a.deleted = false")
  Optional<Assertion> findByNanoId(String nanoId);

  /**
   * Finds an assertion by assertion type and match strategy that is not deleted.
   *
   * @param assertionType the assertion type to filter by
   * @param matchStrategy the match strategy to filter by
   * @return an Optional containing the assertion if found and not deleted, empty otherwise
   */
  @Query(
      "SELECT a FROM Assertion a WHERE a.assertionType = :assertionType AND a.matchStrategy = :matchStrategy AND a.deleted = false")
  Optional<Assertion> findByAssertionTypeAndMatchStrategy(
      AssertionType assertionType, MatchStrategy matchStrategy);

  /**
   * Finds all assertions by their nanoIds.
   *
   * @param nanoIds the list of nanoIds to search for
   * @return a list of assertions matching the provided nanoIds
   */
  @Query("SELECT a FROM Assertion a WHERE a.nanoId IN :nanoIds")
  List<Assertion> findAllByNanoIds(@Param("nanoIds") List<String> nanoIds);
}
