/*

 * Copyright (c) 2024 Vwaves Technologies Private Limited

 *

 * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

 * Product: Aspose.Total for Java

 * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

 * Order ID: 250610115636

 *

 * This source code is part of a proprietary software system and may not be

 * distributed, modified, or reused without explicit written permission from

 * Vwaves Technologies Private Limited.

 *

 * Contact: <EMAIL>

 */
package com.enttribe.promptanalyzer.config.redis;

import com.enttribe.promptanalyzer.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.jetbrains.annotations.NotNull;
import org.springframework.ai.embedding.BatchingStrategy;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.embedding.TokenCountBatchingStrategy;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.redis.RedisVectorStore;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import redis.clients.jedis.DefaultJedisClientConfig;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisClientConfig;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.JedisPooled;
import redis.clients.jedis.JedisSentinelPool;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManagerFactory;
import java.io.FileInputStream;
import java.security.KeyStore;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.time.Duration;
import java.util.Set;
import java.util.stream.Collectors;

@Configuration
@EnableConfigurationProperties({RedisProperties.class})
@Slf4j
@ConditionalOnProperty(name = "vector.store.type", havingValue = "redis", matchIfMissing = true)
public class RedisVectorConfig {

    private static final int TIME_BETWEEN_EVICTION_RUNS_MILLIS = 30000;
    private static final int MIN_EVICTABLE_IDLE_DURATION_MILLIS = 60000;

    // Redis client timeout constants
    private static final int DEFAULT_CONNECTION_TIMEOUT_MILLIS = 5000;
    private static final int DEFAULT_SOCKET_TIMEOUT_MILLIS = 3000;

    @Value("${vector.redis.trustStorePath:}")
    private String trustStorePath;

    @Value("${vector.redis.trustStorePassword:}")
    private String trustStorePassword;

    @Value("${commons.ai.sdk.keyStoreInstance:jks}")
    private String keyStoreInstance;

    @Value("${spring.ai.vectorstore.redis.index-name}")
    private String indexName;

    @Value("${spring.ai.vectorstore.redis.prefix}")
    private String redisPrefix;

    @Value("${spring.data.redis.ssl.enable}")
    private boolean sslEnable;

    @Value("${spring.data.redis.timeout.connection}")
    private int connectionTimeout;

    @Value("${spring.data.redis.timeout.socket}")
    private int socketTimeout;

    @Value("${spring.data.redis.timeout.blocking}")
    private int blockingTimeout;

    @Value("${spring.data.redis.timeout.read}")
    private int readTimeout;

    @Value("${spring.data.redis.pool.max-total}")
    private int maxTotal;

    @Value("${spring.data.redis.pool.max-idle}")
    private int maxIdle;

    @Value("${spring.data.redis.pool.min-idle}")
    private int minIdle;

    @Bean
    @ConditionalOnMissingBean(BatchingStrategy.class)
    BatchingStrategy batchingStrategy() {
        return new TokenCountBatchingStrategy();
    }

    @Bean
    @Primary
    public VectorStore vectorStore(JedisPooled jedisPooled, EmbeddingModel embeddingModel) {
        log.info("creating redis vector store");
        RedisVectorStore redisVectorStore =
                RedisVectorStore.builder(jedisPooled, embeddingModel)
                        .indexName(indexName)
                        .prefix(redisPrefix)
                        .metadataFields(
                                RedisVectorStore.MetadataField.tag("doc_id"),
                                RedisVectorStore.MetadataField.tag("custom_agent_id"))
                        .initializeSchema(true)
                        .batchingStrategy(new TokenCountBatchingStrategy())
                        .build();

        redisVectorStore.afterPropertiesSet();
        return redisVectorStore;
    }

    @Bean
    @ConditionalOnProperty(name = "spring.data.redis.ssl.enable", havingValue = "true", matchIfMissing = true)
    public JedisPooled jedisPooled(RedisProperties redisProperties) {
        log.info("Creating JedisPooled with configuration - connectionTimeout: {}, socketTimeout: {}, blockingTimeout: {}, readTimeout: {}, maxTotal: {}, maxIdle: {}, minIdle: {}",
                connectionTimeout, socketTimeout, blockingTimeout, readTimeout, maxTotal, maxIdle, minIdle);
        String masterName = redisProperties.getSentinel().getMaster();

        // Convert nodes to HostAndPort set
        Set<HostAndPort> sentinelNodes = redisProperties.getSentinel().getNodes().stream()
                .map(HostAndPort::from)
                .collect(Collectors.toSet());

        log.info("Configuring Redis Sentinel connection. Master: {}, Sentinels: {}", masterName, sentinelNodes);

        // Pool config
        GenericObjectPoolConfig<Jedis> poolConfig = getJedisGenericObjectPoolConfig();

        // --- Master client config ---
        DefaultJedisClientConfig.Builder masterConfigBuilder = DefaultJedisClientConfig.builder()
                .connectionTimeoutMillis(DEFAULT_CONNECTION_TIMEOUT_MILLIS)
                .socketTimeoutMillis(DEFAULT_SOCKET_TIMEOUT_MILLIS)
                .password(redisProperties.getPassword())
                .database(redisProperties.getDatabase());

        if (sslEnable) {
            log.info("Setting up SSL for Redis master connection");
            SSLSocketFactory sslSocketFactory = createSSLSocketFactory();
            masterConfigBuilder.ssl(true)
                    .sslSocketFactory(sslSocketFactory);
        }
        JedisClientConfig masterClientConfig = masterConfigBuilder.build();

        // --- Sentinel client config ---
        DefaultJedisClientConfig.Builder sentinelConfigBuilder = DefaultJedisClientConfig.builder()
                .connectionTimeoutMillis(DEFAULT_CONNECTION_TIMEOUT_MILLIS)
                .socketTimeoutMillis(DEFAULT_SOCKET_TIMEOUT_MILLIS)
                .password(redisProperties.getPassword()); // required for "requirepass" in Sentinel

        if (sslEnable) {
            log.info("Setting up SSL for Sentinel connection");
            SSLSocketFactory sslSocketFactory = createSSLSocketFactory();
            sentinelConfigBuilder
                    .ssl(true)
                    .sslSocketFactory(sslSocketFactory);
        }
        JedisClientConfig sentinelClientConfig = sentinelConfigBuilder.build();

        // Create Sentinel pool with configs
        try (JedisSentinelPool sentinelPool = new JedisSentinelPool(
                masterName,
                sentinelNodes,
                poolConfig,
                masterClientConfig,
                sentinelClientConfig
        )) {
            // Get the current master address
            HostAndPort currentMaster = sentinelPool.getCurrentHostMaster();
            log.info("Redis Sentinel resolved master to: {}", currentMaster);

            // Create JedisPooled using resolved master
            return new JedisPooled(currentMaster, masterClientConfig);
        } catch (Exception e) {
            log.error("Error initializing JedisSentinelPool", e);
            throw e;
        }
    }

    @NotNull
    private static GenericObjectPoolConfig<Jedis> getJedisGenericObjectPoolConfig() {
        GenericObjectPoolConfig<Jedis> poolConfig = new GenericObjectPoolConfig<>();
        poolConfig.setBlockWhenExhausted(true);
        poolConfig.setTestWhileIdle(true);
        poolConfig.setTimeBetweenEvictionRuns(Duration.ofMillis(TIME_BETWEEN_EVICTION_RUNS_MILLIS));
        poolConfig.setMinEvictableIdleDuration(Duration.ofMillis(MIN_EVICTABLE_IDLE_DURATION_MILLIS));
        poolConfig.setTestOnBorrow(true);
        poolConfig.setTestOnReturn(false);
        return poolConfig;
    }

    private SSLSocketFactory createSSLSocketFactory() {
        try {
            CertificateFactory cf = CertificateFactory.getInstance("X.509");

            X509Certificate caCert;
            try (FileInputStream fis = new FileInputStream(trustStorePath)) {
                caCert = (X509Certificate) cf.generateCertificate(fis);
            }

            KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
            keyStore.load(null, null);
            keyStore.setCertificateEntry("redis8-ca", caCert);

            TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            tmf.init(keyStore);

            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, tmf.getTrustManagers(), null);

            return sslContext.getSocketFactory();
        } catch (Exception e) {
            throw new BusinessException("Failed to create SSL socket factory for Redis connection", e);
        }
    }

    @Bean
    @ConditionalOnProperty(name = "spring.data.redis.ssl.enable", havingValue = "false")
    public JedisPooled jedisPooledLocal(RedisProperties redisProperties) {
        log.info("Creating JedisPooled for local with configuration - connectionTimeout: {}, socketTimeout: {}, blockingTimeout: {}, readTimeout: {}, maxTotal: {}, maxIdle: {}, minIdle: {}",
                connectionTimeout, socketTimeout, blockingTimeout, readTimeout, maxTotal, maxIdle, minIdle);
        JedisConnectionFactory jedisConnectionFactory = this.getJedisConnectionFactory(redisProperties);
        return this.jedisPooled(jedisConnectionFactory);
    }

    private JedisPooled jedisPooled(JedisConnectionFactory jedisConnectionFactory) {
        String host = jedisConnectionFactory.getHostName();
        int port = jedisConnectionFactory.getPort();
        JedisClientConfig clientConfig =
                DefaultJedisClientConfig.builder()
                        .clientName(jedisConnectionFactory.getClientName())
                        .socketTimeoutMillis(socketTimeout)
                        .connectionTimeoutMillis(connectionTimeout)
                        .blockingSocketTimeoutMillis(blockingTimeout)
                        .password(jedisConnectionFactory.getPassword())
                        .build();

        return new JedisPooled(new HostAndPort(host, port), clientConfig);
    }


    private JedisConnectionFactory getJedisConnectionFactory(RedisProperties redisProperties) {
        RedisStandaloneConfiguration standaloneConfig = new RedisStandaloneConfiguration();
        standaloneConfig.setHostName(redisProperties.getHost());
        standaloneConfig.setPort(redisProperties.getPort());
        standaloneConfig.setDatabase(redisProperties.getDatabase());

        if (redisProperties.getPassword() != null) {
            standaloneConfig.setPassword(redisProperties.getPassword());
        }

        JedisPoolConfig poolConfig = getJedisPoolConfig();

        JedisClientConfiguration clientConfig = JedisClientConfiguration.builder().usePooling().poolConfig(poolConfig).and().connectTimeout(Duration.ofMillis(connectionTimeout)).readTimeout(Duration.ofMillis(readTimeout)).build();
        return new JedisConnectionFactory(standaloneConfig, clientConfig);
    }

    @NotNull
    private JedisPoolConfig getJedisPoolConfig() {
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxTotal(maxTotal);
        poolConfig.setMaxIdle(maxIdle);
        poolConfig.setMinIdle(minIdle);
        poolConfig.setTestOnBorrow(true);
        poolConfig.setTestWhileIdle(true);
        poolConfig.setTimeBetweenEvictionRuns(Duration.ofMillis(TIME_BETWEEN_EVICTION_RUNS_MILLIS));
        poolConfig.setMinEvictableIdleDuration(Duration.ofMillis(MIN_EVICTABLE_IDLE_DURATION_MILLIS));
        poolConfig.setNumTestsPerEvictionRun(-1);
        return poolConfig;
    }
}
