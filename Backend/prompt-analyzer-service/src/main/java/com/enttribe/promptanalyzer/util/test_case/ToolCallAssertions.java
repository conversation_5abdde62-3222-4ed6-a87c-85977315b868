/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util.test_case;

import static org.hibernate.validator.internal.util.Contracts.assertTrue;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;

import com.enttribe.promptanalyzer.enums.AssertionType;
import com.enttribe.promptanalyzer.enums.MatchStrategy;
import com.enttribe.promptanalyzer.model.AssertionResult;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Utility class for performing assertions on tool call responses and JSON structures.
 *
 * <p>This class provides static methods for validating tool call responses, including
 * tool name assertions, parameter value assertions, and JSON structure validation.
 * It supports various match strategies (EXACT, CONTAINS, REGEX) and provides detailed
 * assertion results with accuracy scores.</p>
 *
 * <p>The utility methods handle JSON parsing, error handling, and provide comprehensive
 * logging for debugging purposes. They are designed to work with tool call responses
 * in various formats including single objects and arrays.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 * @see AssertionResult
 * @see AssertionType
 * @see MatchStrategy
 */
public class ToolCallAssertions {

  /** ObjectMapper instance for JSON parsing operations. */
  private static final ObjectMapper objectMapper = new ObjectMapper();
  
  /** Logger instance for this class. */
  private static final Logger log = LoggerFactory.getLogger(ToolCallAssertions.class);
  
  /** Constant for error message formatting. */
  private static final String AND_EXPECTED_VALUE = " and expected value : '";
  
  /** Constant for assertion passed message. */
  private static final String ASSERTION_PASSED = "assertion passed";
  
  /** Constant for arguments field name. */
  private static final String ARGUMENTS = "arguments";
  
  /** Constant for percentage calculation (100%). */
  private static final double PERCENTAGE_MULTIPLIER = 100.0;

  /**
   * Private constructor to prevent instantiation of this utility class.
   *
   * @throws IllegalStateException if an attempt is made to instantiate this class
   */
  private ToolCallAssertions() {
    throw new IllegalStateException("utility class can not be instantiated");
  }

  /**
   * Asserts that the tool name matches the expected name.
   *
   * @param jsonString the JSON string
   * @param expectedName the expected tool name
   */
  public static AssertionResult assertToolName(
      String jsonString, String expectedName, String userQuestion) {
    String actualName = "";
    try {
      actualName = getToolName(jsonString);
      assertEquals("Tool name does not match", expectedName, actualName);
      return AssertionResult.builder()
          .actual(actualName)
          .expected(expectedName)
          .isPassed(true)
          .userQuestion(userQuestion)
          .assertionType(AssertionType.TOOL_CALLED.toString())
          .matchStrategy(MatchStrategy.EXACT.toString())
          .comment(ASSERTION_PASSED)
          .accuracyScore(PERCENTAGE_MULTIPLIER)
          .createdTime(new Date())
          .build();
    } catch (AssertionError | Exception e) {
      log.error("error in assertToolName : {}", e.getMessage());
      return AssertionResult.builder()
          .actual(actualName)
          .expected(expectedName)
          .isPassed(false)
          .userQuestion(userQuestion)
          .assertionType(AssertionType.TOOL_CALLED.toString())
          .matchStrategy(MatchStrategy.EXACT.toString())
          .comment(e.getMessage())
          .accuracyScore(0.0)
          .createdTime(new Date())
          .build();
    }
  }

  /**
   * Extracts the tool name from a JSON string representing a tool call.
   *
   * <p>This method parses the JSON string and extracts the tool name from either
   * a single object or an array format. It handles both formats gracefully and
   * returns an empty string if parsing fails.</p>
   *
   * @param jsonString the JSON string containing tool call information
   * @return the tool name, or empty string if parsing fails
   */
  public static String getToolName(String jsonString) {
    try {
      JsonNode root = objectMapper.readTree(jsonString);
      if (jsonString.startsWith("{")) {
        return root.get("name").asText();
      }
      return root.get(0).get("name").asText();
    } catch (Exception e) {
      log.error("error in getToolName for json : {}: {}", jsonString, e.getMessage());
      return "";
    }
  }

  /**
   * Asserts a specific value inside a nested JSON string using JSON Pointer and match
   * matchStrategy.
   *
   * @param jsonString the JSON string
   * @param jsonPointer the JSON Pointer path (e.g., "/filters/0/conditions/0/parameterValue")
   * @param expectedValue the expected value or pattern
   * @param matchStrategy the match matchStrategy (EXACT, CONTAINS, REGEX, SUBSET)
   */
  public static AssertionResult assertJsonArgument(
      String jsonString,
      String jsonPointer,
      String expectedValue,
      MatchStrategy matchStrategy,
      String userQuestion) {
    jsonPointer = "/" + jsonPointer;
    String rawActualValue = "";
    try {
      JsonNode jsonNode = objectMapper.readTree(jsonString);
      String arguments = jsonNode.get(0).get(ARGUMENTS).asText();
      JsonNode root = objectMapper.readTree(arguments);
      JsonNode node = root.at(jsonPointer);

      assertFalse("Path not found: " + jsonPointer, node.isMissingNode());
      String actualValue = node.toString();
      rawActualValue = node.isTextual() ? node.asText() : actualValue;

      switch (matchStrategy) {
        case EXACT -> assertEquals(
            "EXACT match failed at " + jsonPointer + "\n", expectedValue, rawActualValue);
        case CONTAINS -> assertTrue(
            rawActualValue.contains(expectedValue),
            "CONTAINS match failed at "
                + jsonPointer
                + ". Actual value : '"
                + rawActualValue
                + AND_EXPECTED_VALUE
                + expectedValue
                + "'\n");
        case REGEX -> assertTrue(
            rawActualValue.matches(expectedValue),
            "REGEX match failed at "
                + jsonPointer
                + ". Actual value is : '"
                + rawActualValue
                + AND_EXPECTED_VALUE
                + expectedValue
                + "'\n");
        default -> throw new UnsupportedOperationException(
            "Unsupported match matchStrategy: " + matchStrategy);
      }
      return AssertionResult.builder()
          .actual(rawActualValue)
          .expected(expectedValue)
          .parameterName(jsonPointer.substring(1))
          .isPassed(true)
          .userQuestion(userQuestion)
          .assertionType(AssertionType.TOOL_PARAMETERS.toString())
          .matchStrategy(matchStrategy.toString())
          .comment(ASSERTION_PASSED)
          .accuracyScore(PERCENTAGE_MULTIPLIER)
          .createdTime(new Date())
          .build();
    } catch (AssertionError | Exception e) {
      log.error("error in assertJsonArgument : {}", e.getMessage());
      return AssertionResult.builder()
          .actual(rawActualValue)
          .expected(expectedValue)
          .parameterName(jsonPointer.substring(1))
          .isPassed(false)
          .userQuestion(userQuestion)
          .assertionType(AssertionType.TOOL_PARAMETERS.toString())
          .matchStrategy(matchStrategy.toString())
          .comment(e.getMessage())
          .accuracyScore(0.0)
          .createdTime(new Date())
          .build();
    }
  }

  /**
   * Extracts a parameter value from a tool call JSON using a JSON pointer path.
   *
   * <p>This method navigates through the JSON structure using the provided JSON pointer
   * to extract the value at the specified path. It handles nested structures and
   * returns the original JSON string if the path is not found or parsing fails.</p>
   *
   * @param toolCallJson the JSON string containing tool call information
   * @param jsonPointer the JSON pointer path to the parameter (without leading slash)
   * @return the parameter value, or the original JSON string if extraction fails
   */
  public static String getParameterValue(String toolCallJson, String jsonPointer) {
    try {
      jsonPointer = "/" + jsonPointer;
      JsonNode jsonNode = objectMapper.readTree(toolCallJson);
      String arguments = jsonNode.get(0).get(ARGUMENTS).asText();
      JsonNode root = objectMapper.readTree(arguments);
      JsonNode node = root.at(jsonPointer);
      if (node.isEmpty()) {
        return toolCallJson;
      } else if (node.isTextual()) {
        return node.asText();
      } else {
        return node.toString();
      }
    } catch (Exception e) {
      log.error("error in getParameterValue : {}, toolCallJson : {}, exception : {}", jsonPointer, toolCallJson, e.getMessage());
      return toolCallJson;
    }
  }

  /**
   * Determines if a tool was invoked in the given response JSON.
   *
   * <p>This method analyzes the response JSON to check if it contains a valid tool
   * invocation. It looks for the presence of required fields (type, name, arguments)
   * and validates that they are not empty. The method supports both array and object
   * response formats.</p>
   *
   * @param responseJson the JSON string to analyze for tool invocation
   * @return true if a tool was invoked, false otherwise
   */
  public static boolean isToolInvoked(String responseJson) {
    try {
      JsonNode root = objectMapper.readTree(responseJson);
      if (root.isArray() && !root.isEmpty()) {
        for (JsonNode node : root) {
          boolean isFunction = "function".equalsIgnoreCase(node.path("type").asText());
          boolean hasName = node.hasNonNull("name") && !node.get("name").asText().isEmpty();
          boolean hasArguments =
              node.hasNonNull(ARGUMENTS) && !node.get(ARGUMENTS).asText().isEmpty();
          if (isFunction && hasName && hasArguments) {
            return true;
          }
        }
      } else if (root.isObject()) {
        return root.hasNonNull("name") && !root.get("name").asText().isEmpty();
      }
    } catch (Exception e) {
      log.error("error in isToolInvoked. response : {}", responseJson);
    }
    return false;
  }
}
