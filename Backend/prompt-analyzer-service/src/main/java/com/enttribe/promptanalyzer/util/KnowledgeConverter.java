/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.KnowledgeBase;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;
import lombok.extern.slf4j.Slf4j;

/**
 * Utility class for converting KnowledgeBase entities to KnowledgeBaseResponseDto objects. Provides
 * methods to convert individual entities and lists of entities.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public final class KnowledgeConverter {

  /**
   * Private constructor to prevent instantiation of this utility class.
   */
  private KnowledgeConverter() {}

  /** Constant for splitting S3 file paths. */
  private static final int SPLIT_LIMIT = 2;

  /**
   * Converts a KnowledgeBase entity to a KnowledgeBaseResponseDto object.
   *
   * @param knowledgeBase the KnowledgeBase entity to convert
   * @return the converted KnowledgeBaseResponseDto object, or null if the input is null
   */
  public static KnowledgeBaseResponseDto convertKnowledgeDto(KnowledgeBase knowledgeBase) {
    if (knowledgeBase == null) {
      return null;
    }

    try {
      return KnowledgeBaseResponseDto.builder()
          .id(knowledgeBase.getNanoId())
          .name(knowledgeBase.getName())
          .description(knowledgeBase.getDescription())
          .docId(knowledgeBase.getDocId())
          .docMetaData(knowledgeBase.getDocMetaData())
          .vectorMetaData(knowledgeBase.getVectorMetaData())
          .filter(knowledgeBase.getFilter())
          .isContext(knowledgeBase.getIsContext())
          .topK(knowledgeBase.getTopK())
          .similarityThreshold(knowledgeBase.getSimilarityThreshold())
          .type(knowledgeBase.getType())
          .className(knowledgeBase.getClassName())
          .fileName(knowledgeBase.getFileName())
          .websiteUrl(knowledgeBase.getWebSiteUrl())
          .collectionName(knowledgeBase.getCollectionName())
          .tags(knowledgeBase.getTag())
          .tables(JsonUtils.convertJsonToList(knowledgeBase.getTables(), String.class))
          .integration(knowledgeBase.getIntegration())
          .returnDirect(knowledgeBase.getReturnDirect())
          .s3FileNames(
              s3FileConvertToJson(knowledgeBase.getS3FileNames(), knowledgeBase.getFileName()))
          .build();
    } catch (JsonProcessingException e) {
      log.warn("Unable to convert KnowledgeBase entity to DTO: {}", e.getMessage(), e);
      throw new BusinessException("Failed to map Entity to DTO");
    }
  }

  /**
   * Converts a list of KnowledgeBase entities to a list of KnowledgeBaseResponseDto objects.
   *
   * @param knowledgeBases the list of KnowledgeBase entities to convert
   * @return a list of converted KnowledgeBaseResponseDto objects, or an empty list if the input is
   *     null or empty
   */
  public static List<KnowledgeBaseResponseDto> getKnowledgeBasesDtoList(
      List<KnowledgeBase> knowledgeBases) {
    if (knowledgeBases == null || knowledgeBases.isEmpty()) {
      return List.of();
    }

    return knowledgeBases.stream().map(KnowledgeConverter::convertKnowledgeDto).toList();
  }

  /**
   * Converts S3 file names and file names to a JSON format with label-value pairs.
   *
   * <p>This method takes S3 file names (which may be a JSON string or comma-separated list)
   * and file names, then creates a structured JSON format where each entry has a label
   * (display name) and value (S3 path). It handles various input formats and provides
   * fallback file name extraction from S3 paths.</p>
   *
   * @param s3FileNames the S3 file names (JSON string or comma-separated)
   * @param fileName the file names (comma-separated)
   * @return JSON string with label-value pairs, or original s3FileNames if conversion fails
   * @throws BusinessException if JSON conversion fails
   */
  private static String s3FileConvertToJson(String s3FileNames, String fileName) {

    if (s3FileNames == null || s3FileNames.isBlank() || fileName == null || fileName.isBlank()) {
      return s3FileNames;
    }
    try {
      // Check if s3FileNames is already a JSON string and deserialize it first
      List<String> s3List =
          JsonUtils.convertJsonToList(s3FileNames, String.class); // Deserialize if it's a string
      List<String> fileNameList =
          Arrays.asList(fileName.split("\\s{0,5},\\s{0,5}")); // handles optional spaces

      // Merge label and value preserving label first and value second
      List<Map<String, String>> merged =
          IntStream.range(0, s3List.size())
              .mapToObj(
                  i ->
                      Map.of(
                          "label",
                          i < fileNameList.size()
                              ? fileNameList.get(i)
                              : extractFileName(s3List.get(i)),
                          "value",
                          s3List.get(i) // Ensure value comes second
                          ))
              .toList();

      // Convert the merged list to JSON and return it
      return JsonUtils.convertToJSON(merged); // This will serialize it correctly into JSON format
    } catch (Exception e) {
      throw new BusinessException("Failed to convert s3FileNames to JSON: " + e.getMessage());
    }
  }

  /**
   * Extracts a file name from an S3 path by splitting on underscore.
   *
   * <p>This method splits the S3 path on the first underscore and returns the second part
   * as the file name. If the split doesn't produce two parts, it returns the original path.</p>
   *
   * @param s3Path the S3 path to extract file name from
   * @return the extracted file name or the original path if extraction fails
   */
  private static String extractFileName(String s3Path) {
    String[] parts = s3Path.split("_", SPLIT_LIMIT);
    return parts.length > 1 ? parts[1] : s3Path;
  }
}
