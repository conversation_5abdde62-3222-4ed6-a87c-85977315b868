/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.conversationalai.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * Utility class for making HTTP requests with support for authentication and custom headers.
 *
 * <p>This class provides static methods for sending HTTP GET and POST requests with
 * various configurations including bearer token authentication, custom headers, and
 * request bodies. It uses {@link HttpURLConnection} for making the actual HTTP requests.</p>
 *
 * <p>Key features:</p>
 * <ul>
 *   <li>Support for GET and POST requests</li>
 *   <li>Bearer token authentication</li>
 *   <li>Custom header support</li>
 *   <li>Request body handling for POST requests</li>
 *   <li>Automatic response reading</li>
 *   <li>Error stream handling for non-2xx responses</li>
 * </ul>
 *
 * <p><strong>Thread Safety:</strong> This class is thread-safe as it only contains
 * static methods and constants.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public class HttpUtils {

  /** Prefix for Bearer token authentication. */
  private static final String BEARER_PREFIX = "Bearer ";
  
  /** Read timeout in milliseconds (10 minutes). */
  private static final int READ_TIMEOUT_MS = 600000; // 10 minutes
  
  /** Header name for access token. */
  public static final String ACCESS_TOKEN = "Access-Token";
  
  /** Header name for content type. */
  public static final String CONTENT_TYPE = "Content-Type";
  
  /** Content type value for JSON. */
  public static final String APPLICATION_JSON = "application/json";

  /**
   * Private constructor to prevent instantiation of this utility class.
   *
   * @throws IllegalStateException if an attempt is made to instantiate this class
   */
  private HttpUtils() {
    throw new IllegalStateException("Utility class");
  }

  /**
   * Creates an HTTP connection with the specified URL, method, and optional token.
   *
   * <p>This method creates and configures an {@link HttpURLConnection} with the given
   * parameters. It validates the URL using {@link URI#create(String)} and sets up
   * authentication and content type headers.</p>
   *
   * @param urlString the URL string to connect to
   * @param method the HTTP method (GET, POST, etc.)
   * @param token the bearer token for authentication (can be null)
   * @return configured HttpURLConnection
   * @throws IOException if there's an error creating the connection
   */
  private static HttpURLConnection createConnection(String urlString, String method, String token)
      throws IOException {
    // Use URI to parse and validate, then convert to URL
    URI uri = URI.create(urlString);
    URL url = uri.toURL();

    HttpURLConnection connection = (HttpURLConnection) url.openConnection();
    connection.setRequestMethod(method);
    if (token != null) {
      connection.setRequestProperty("Authorization", BEARER_PREFIX + token);
    }
    connection.setRequestProperty(CONTENT_TYPE, APPLICATION_JSON);
    return connection;
  }

  /**
   * Sends an internal GET request with custom headers and authentication token.
   *
   * <p>This method is specifically designed for internal service-to-service communication.
   * It uses the internal connection creation method and supports custom headers along
   * with bearer token authentication.</p>
   *
   * @param urlString the URL to send the GET request to
   * @param headers map of custom headers to include in the request
   * @param token the bearer token for authentication
   * @return the response body as a string
   * @throws IOException if there's an error during the request
   */
  public static String sendInternalGet(String urlString, Map<String, String> headers, String token)
      throws IOException {
    HttpURLConnection connection = createInternalConnection(urlString, "GET", token);
    if (headers != null) {
      for (Map.Entry<String, String> header : headers.entrySet()) {
        connection.setRequestProperty(header.getKey(), header.getValue());
      }
    }
    return readResponse(connection);
  }

  private static HttpURLConnection createInternalConnection(
      String urlString, String method, String token) throws IOException {
    URL url = URI.create(urlString).toURL();
    HttpURLConnection connection = (HttpURLConnection) url.openConnection();
    connection.setRequestMethod(method);
    if (token != null) {
      connection.setRequestProperty(ACCESS_TOKEN, BEARER_PREFIX + token);
    }
    connection.setRequestProperty(CONTENT_TYPE, APPLICATION_JSON);
    return connection;
  }

  private static String readResponse(HttpURLConnection connection) throws IOException {
    int responseCode = connection.getResponseCode();
    try (BufferedReader reader =
        new BufferedReader(
            new InputStreamReader(
                responseCode >= 200 && responseCode < 300
                    ? connection.getInputStream()
                    : connection.getErrorStream()))) {

      StringBuilder response = new StringBuilder();
      String line;
      while ((line = reader.readLine()) != null) {
        response.append(line);
      }
      return response.toString();
    }
  }

  /**
   * Sends a GET request with custom headers.
   *
   * @param urlString the URL to send the GET request to
   * @param headers map of custom headers to include in the request
   * @return the response body as a string
   * @throws IOException if there's an error during the request
   */
  public static String sendGet(String urlString, Map<String, String> headers) throws IOException {
    HttpURLConnection connection = createConnection(urlString, "GET", null);
    if (headers != null) {
      for (Map.Entry<String, String> header : headers.entrySet()) {
        connection.setRequestProperty(header.getKey(), header.getValue());
      }
    }
    return readResponse(connection);
  }

  public static String sendGet(String urlString, Map<String, String> headers, String token)
      throws IOException {
    HttpURLConnection connection = createConnection(urlString, "GET", token);
    if (headers != null) {
      for (Map.Entry<String, String> header : headers.entrySet()) {
        connection.setRequestProperty(header.getKey(), header.getValue());
      }
    }
    return readResponse(connection);
  }

  /**
   * Sends a POST request with a request body and authentication token.
   *
   * <p>This method sends a POST request with the specified body content and bearer token
   * authentication. It sets the appropriate content type and handles the request body
   * writing.</p>
   *
   * @param urlString the URL to send the POST request to
   * @param body the request body content
   * @param token the bearer token for authentication
   * @return the response body as a string
   * @throws IOException if there's an error during the request
   */
  public static String sendPost(String urlString, String body, String token) throws IOException {
    HttpURLConnection connection = createConnection(urlString, "POST", token);
    connection.setDoOutput(true);
    connection.setReadTimeout(READ_TIMEOUT_MS);
    if (body != null && !body.isEmpty()) {
      try (OutputStream os = connection.getOutputStream()) {
        os.write(body.getBytes(StandardCharsets.UTF_8));
        os.flush();
      }
    }
    return readResponse(connection);
  }

  public static String sendPost(String urlString, String body, Map<String, String> headers)
      throws IOException {

    HttpURLConnection connection = createConnection(urlString, "POST", null);
    connection.setDoOutput(true);

    if (headers != null) {
      for (Map.Entry<String, String> header : headers.entrySet()) {
        connection.setRequestProperty(header.getKey(), header.getValue());
      }
    }

    if (body != null && !body.isEmpty()) {
      try (OutputStream os = connection.getOutputStream()) {
        os.write(body.getBytes(StandardCharsets.UTF_8));
        os.flush();
      }
    }
    return readResponse(connection);
  }
}
