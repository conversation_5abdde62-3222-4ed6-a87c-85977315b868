spring.application.name=prompt-analyzer

server.port=8081
server.servlet.context-path=/prompt-analyzer/rest
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
logging.level.root=ERROR
logging.level.com.enttribe.promptanalyzer=${PROMPT_LOG_LEVEL:DEBUG}
spring.output.ansi.enabled=ALWAYS

base-plateform-service.url=${PLATFORM_URL:http://base-utility-service/base/util/rest}
prompt.audit.enable=true

## Database properties
commons.datasource.url=${MYSQL_URL:FYcWAdAcjuATOVvKyBddON7wOchbrNg2DIP3UaEq1XCKc03t/R1Fy/Zt+sNLtQijErtMJcdX/OOUzu5O3Qe9dIsi3T2irKG5XTSsH4dOI6y10P6gKjzSlxNS0kb9Z9MUViD1CvHZ0QYHi3xkG7VhhPjHh96iVhD0vtqNkR8oBpPko5VQHOHR9Rvhp7RcX8/PQATiKMWMKDPdbKov36ES81lPK8SOGnFOAFv8vQX8bCE=}
commons.datasource.username=${MYSQL_USERNAME:b1WzuBrMOr8p6+bbnCq8iA==}
commons.datasource.checkSum=${MYSQL_CHECKSUM:b1WzuBrMOr8p6+bbnCq8iA==}

commons.datasource.driverClassName=${MYSQL_DRIVER:org.mariadb.jdbc.Driver}
commons.datasource.minIdle=${MYSQL_MIN_IDLE:20}
commons.datasource.maxPoolSize=${MYSQL_MAX_POOL_SIZE:100}
commons.datasource.connectionTimeout=${MYSQL_CONNECTION_TIMEOUT:30000}

commons.datasource.ssl.keystore.path=${MYSQL_KEYSTORE_PATH:/opt/visionwaves/sql_ssl/keystore.p12}
commons.datasource.ssl.truststore.path=${MYSQL_TRUSTSTORE_PATH:/opt/visionwaves/sql_ssl/truststore.p12}
commons.datasource.ssl.keystore.password=${MYSQL_KEYSTORE_CHECKSUM:keystore}
commons.datasource.ssl.truststore.password=${MYSQL_TRUSTSTORE_CHECKSUM:keystore}
commons.datasource.ssl.enabled=${SSL_ENABLE:false}

## Hibernate properties
spring.jpa.hibernate.ddl-auto=none
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.properties.hibernate.connection.isolation=2
spring.datasource.hikari.data-source-properties.isolation=READ_COMMITTED

## LLM properties
spring.ai.openai.api-key=${CHAT_API_KEY:********************************************************}
spring.ai.openai.chat.options.model=${CHAT_MODEL:llama-3.3-70b-versatile}
spring.ai.openai.chat.base-url=${CHAT_BASE_URL:https://api.groq.com/openai/}

spring.ai.openai.embedding.base-url=${EMBEDDING_BASE_URL:https://gaa7ukrp3jkntp1b.us-east-1.aws.endpoints.huggingface.cloud/}
spring.ai.openai.embedding.api-key=${EMBEDDING_API_KEY:*************************************}
spring.ai.openai.embedding.options.model=${EMBEDDING_MODEL:BAAI/bge-base-en-v1.5}

## WEBSITE Crawling properties
crawl.url=${CRAWL_URL:http://192.168.10.3:31796}
crawl.token=${CRAWL_TOKEN:vwaves}

## Redis common properties
spring.data.redis.ssl.enable=${REDIS_SSL:false}
spring.data.redis.username=${REDIS_USERNAME:default}
spring.data.redis.timeout.connection=${REDIS_CONNECTION_TIMEOUT:10000}
spring.data.redis.timeout.socket=${REDIS_SOCKET_TIMEOUT:10000}
spring.data.redis.timeout.blocking=${REDIS_BLOCKING_TIMEOUT:10000}
spring.data.redis.pool.max-total=${REDIS_POOL_MAX_TOTAL:50}
spring.data.redis.pool.max-idle=${REDIS_POOL_MAX_IDLE:10}
spring.data.redis.pool.min-idle=${REDIS_POOL_MIN_IDLE:4}

## Redis properties
vector.redis.trustStorePath=${REDIS_TRUST_STORE_PATH:/opt/visionwaves/sql_ssl/redis_ca.pem}
vector.redis.trustStorePassword=${REDIS_TRUSTSTORE_CHECKSUM:promptRunner}
spring.data.redis.password=${REDIS_CHECKSUM:}
spring.data.redis.timeout.read=${REDIS_READ_TIMEOUT:10000}
spring.data.redis.host=${REDIS_HOST:localhost}
spring.data.redis.port=${REDIS_PORT:6379}

spring.data.redis.sentinel.master=${REDIS_SENTINEL_MASTER:mymaster}
spring.data.redis.sentinel.nodes=${REDIS_SENTINEL_NODES:redis-node-0.redis-headless.vector-redis.svc.cluster.local:26379,redis-node-1.redis-headless.vector-redis.svc.cluster.local:26379,redis-node-2.redis-headless.vector-redis.svc.cluster.local:26379}

## Redis configuration
vector.store.type=${VECTOR_STORE_TYPE:redis}
spring.ai.vectorstore.redis.index-name=${REDIS_INDEX_NAME:vector_store_knowledge_base}
spring.ai.vectorstore.redis.prefix=${REDIS_PREFIX:prompt_smith_}


##JavaMelody configuration
javamelody.init-parameters.log=true
javamelody.spring-monitoring-enabled=true
javamelody.enabled=true
javamelody.advisor-auto-proxy-creator-enabled=false
javamelody.init-parameters.storage-directory=/tmp/javamelody/enttribe
javamelody.init-parameters.monitoring-path=/monitoring


# NiFi API URLs
nifi.api.url=${NIFI_API_URL:https://dev.visionwaves.com/nifi-api}
nifi.custom.agent.url=${NIFI_CUSTOM_AGENT_URL:https://dev.visionwaves.com/apim/jos/1.0.2/nifiCustomRest/createNewProcessGroups/Ingest/}

# User Info API URL
userInfo.api.url=${USER_INFO_API_URL:http://base-utility-service/base/util/rest/User/userBasicAttributes}

# processors list API URL
process.groups=/process-groups/
processorList.api.url=${PROCESSOR_LIST_API_URL:http://db-mngr-service.ansible.svc.cluster.local:8420/jos/processor/getCustomAgentProcessors}
#processorList.api.url=${PROCESSOR_LIST_API_URL:https://dev.visionwaves.com/apim/jos/1.0.2/processor/getCustomAgentProcessors}

agent.details.url=${AGENT_DETAILS_URL:http://multi-agent-service.ansible.svc.cluster.local/multi-agent}
execution.api.url=${EXECUTION_API_URL:http://connectx-service/connectx/rest/execution/run}

# S3 (SeaweedFS S3-compatible) Service Configuration
s3Region=${S3_REGION:us-east-2}
s3User=${OBJECT_STORAGE_ACCESS_KEY_ID:bootadmin}
s3Checksum=${OBJECT_STORAGE_SECRET_ACCESS_KEY:bootadmin}
s3Url=${OBJECT_STORAGE_ENDPOINT:http://seaweedfs-s3.swf.svc.cluster.local:8333}
#s3Url=http://localhost:8333
s3BucketName=${OBJECT_STORAGE_BUCKET_NAME:prompt-analyzer}

# Hint service configuration with environment variable overrides
intent.hint-filter=${INTENT_HINT_FILTER:employee}
intent.hint-value=${INTENT_HINT_VALUE:hintValue}
intent.filter-expression-format=${INTENT_FILTER_EXPRESSION_FORMAT:filter == '%s'}
intent.metadata-key=${INTENT_METADATA_KEY:metadata}
intent.default-similarity-threshold=${INTENT_DEFAULT_SIMILARITY_THRESHOLD:0.8}
intent.alt-similarity-threshold=${INTENT_ALT_SIMILARITY_THRESHOLD:0.8}
intent.exact-match-score-threshold=${INTENT_EXACT_MATCH_SCORE_THRESHOLD:0.9}
intent.default-top-k=${INTENT_DEFAULT_TOP_K:5}

prompt.saas.enable=${SAAS_ENABLE:false}

# Micro Intent Service Configuration
micro.intent.api.url=${MICRO_INTENT_API:http://x101-service.ansible.svc.cluster.local/admin-service/micro-intent/search?filter=isDeleted==false&offset=0&size=200&orderBy=modifiedTime&orderType=desc}

